<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Primary Meta Tags -->
    <title>Care Connector - Find Verified Healthcare Professionals</title>
    <meta name="title" content="Care Connector - Find Verified Healthcare Professionals" />
    <meta name="description" content="Connect with verified healthcare professionals and build your personalized care team. Find caregivers, companions, and healthcare professionals in your area." />
    <meta name="keywords" content="healthcare, caregivers, companions, healthcare professionals, care coordination, verified providers, healthcare network" />
    <meta name="author" content="Care Connector" />
    <meta name="robots" content="index, follow" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://careconnector.com/" />
    <meta property="og:title" content="Care Connector - Find Verified Healthcare Professionals" />
    <meta property="og:description" content="Connect with verified healthcare professionals and build your personalized care team. Find caregivers, companions, and healthcare professionals in your area." />
    <meta property="og:image" content="https://careconnector.com/og-image.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://careconnector.com/" />
    <meta property="twitter:title" content="Care Connector - Find Verified Healthcare Professionals" />
    <meta property="twitter:description" content="Connect with verified healthcare professionals and build your personalized care team. Find caregivers, companions, and healthcare professionals in your area." />
    <meta property="twitter:image" content="https://careconnector.com/og-image.jpg" />

    <!-- Accessibility -->
    <meta name="theme-color" content="#10b981" />
    <meta name="color-scheme" content="light" />

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
