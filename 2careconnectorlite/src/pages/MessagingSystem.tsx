import React, { useState, useEffect, useRef } from 'react'
import { supabase } from '../lib/supabase'

interface Message {
  id: string
  sender_id: string
  receiver_id: string
  content: string
  message_type: 'text' | 'image' | 'file'
  read_status: boolean
  created_at: string
  updated_at: string
  sender?: Profile
  receiver?: Profile
}

interface Conversation {
  id: string
  participant_1_id: string
  participant_2_id: string
  created_at: string
  updated_at: string
  last_message?: Message
  participant_1?: Profile
  participant_2?: Profile
  unread_count?: number
}

interface Profile {
  id: string
  email: string
  full_name: string | null
  avatar_url: string | null
  role: string
}

const MessagingSystem: React.FC = () => {
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [messages, setMessages] = useState<Message[]>([])
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null)
  const [newMessage, setNewMessage] = useState('')
  const [loading, setLoading] = useState(true)
  const [messagesLoading, setMessagesLoading] = useState(false)
  const [currentUser, setCurrentUser] = useState<any>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    getCurrentUser()
  }, [])

  useEffect(() => {
    if (currentUser) {
      loadConversations()
    }
  }, [currentUser])

  useEffect(() => {
    if (selectedConversation) {
      loadMessages(selectedConversation.id)
    }
  }, [selectedConversation])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const getCurrentUser = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      setCurrentUser(user)
    } catch (error) {
      console.error('Error getting current user:', error)
    }
  }

  const loadConversations = async () => {
    if (!currentUser) return

    try {
      // Load conversations where current user is a participant
      const { data: conversationsData, error } = await supabase
        .schema('care_connector')
        .from('conversations')
        .select(`
          *,
          participant_1:participant_1_id(id, email, full_name, avatar_url, role),
          participant_2:participant_2_id(id, email, full_name, avatar_url, role)
        `)
        .or(`participant_1_id.eq.${currentUser.id},participant_2_id.eq.${currentUser.id}`)
        .order('updated_at', { ascending: false })

      if (error) throw error

      // Load last message and unread count for each conversation
      const conversationsWithDetails = await Promise.all(
        (conversationsData || []).map(async (conv) => {
          // Get last message
          const { data: lastMessage } = await supabase
            .schema('care_connector')
            .from('messages')
            .select(`
              *,
              sender:sender_id(id, email, full_name, avatar_url, role)
            `)
            .eq('conversation_id', conv.id)
            .order('created_at', { ascending: false })
            .limit(1)
            .single()

          // Get unread count for current user
          const { count: unreadCount } = await supabase
            .schema('care_connector')
            .from('messages')
            .select('*', { count: 'exact' })
            .eq('conversation_id', conv.id)
            .eq('receiver_id', currentUser.id)
            .eq('read_status', false)

          return {
            ...conv,
            last_message: lastMessage,
            unread_count: unreadCount || 0
          }
        })
      )

      setConversations(conversationsWithDetails)
    } catch (error) {
      console.error('Error loading conversations:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadMessages = async (conversationId: string) => {
    setMessagesLoading(true)
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('messages')
        .select(`
          *,
          sender:sender_id(id, email, full_name, avatar_url, role),
          receiver:receiver_id(id, email, full_name, avatar_url, role)
        `)
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true })

      if (error) throw error
      setMessages(data || [])

      // Mark messages as read
      await markMessagesAsRead(conversationId)
    } catch (error) {
      console.error('Error loading messages:', error)
    } finally {
      setMessagesLoading(false)
    }
  }

  const markMessagesAsRead = async (conversationId: string) => {
    if (!currentUser) return

    try {
      await supabase
        .schema('care_connector')
        .from('messages')
        .update({ read_status: true })
        .eq('conversation_id', conversationId)
        .eq('receiver_id', currentUser.id)
        .eq('read_status', false)

      // Refresh conversations to update unread counts
      loadConversations()
    } catch (error) {
      console.error('Error marking messages as read:', error)
    }
  }

  const sendMessage = async () => {
    if (!newMessage.trim() || !selectedConversation || !currentUser) return

    const otherParticipant = selectedConversation.participant_1_id === currentUser.id 
      ? selectedConversation.participant_2_id 
      : selectedConversation.participant_1_id

    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('messages')
        .insert({
          conversation_id: selectedConversation.id,
          sender_id: currentUser.id,
          receiver_id: otherParticipant,
          content: newMessage.trim(),
          message_type: 'text',
          read_status: false
        })
        .select(`
          *,
          sender:sender_id(id, email, full_name, avatar_url, role),
          receiver:receiver_id(id, email, full_name, avatar_url, role)
        `)
        .single()

      if (error) throw error

      // Add message to current messages
      setMessages(prev => [...prev, data])
      setNewMessage('')

      // Update conversation timestamp
      await supabase
        .schema('care_connector')
        .from('conversations')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', selectedConversation.id)

      // Refresh conversations
      loadConversations()
    } catch (error) {
      console.error('Error sending message:', error)
    }
  }

  const startNewConversation = async (participantId: string) => {
    if (!currentUser) return

    try {
      // Check if conversation already exists
      const { data: existingConv } = await supabase
        .schema('care_connector')
        .from('conversations')
        .select('*')
        .or(
          `and(participant_1_id.eq.${currentUser.id},participant_2_id.eq.${participantId}),` +
          `and(participant_1_id.eq.${participantId},participant_2_id.eq.${currentUser.id})`
        )
        .single()

      if (existingConv) {
        // Conversation exists, select it
        const fullConv = conversations.find(c => c.id === existingConv.id)
        if (fullConv) {
          setSelectedConversation(fullConv)
        }
        return
      }

      // Create new conversation
      const { data, error } = await supabase
        .schema('care_connector')
        .from('conversations')
        .insert({
          participant_1_id: currentUser.id,
          participant_2_id: participantId
        })
        .select(`
          *,
          participant_1:participant_1_id(id, email, full_name, avatar_url, role),
          participant_2:participant_2_id(id, email, full_name, avatar_url, role)
        `)
        .single()

      if (error) throw error

      setConversations(prev => [{ ...data, unread_count: 0 }, ...prev])
      setSelectedConversation({ ...data, unread_count: 0 })
    } catch (error) {
      console.error('Error starting new conversation:', error)
    }
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const filteredConversations = conversations.filter(conv => {
    if (!searchQuery) return true
    
    const otherParticipant = conv.participant_1_id === currentUser?.id 
      ? conv.participant_2 
      : conv.participant_1

    return (
      otherParticipant?.full_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      otherParticipant?.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      conv.last_message?.content.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })

  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit',
        hour12: true 
      })
    } else if (diffInHours < 48) {
      return 'Yesterday'
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      })
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="text-center">
          <div className="inline-flex items-center gap-3 px-8 py-6 rounded-xl shadow-lg" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
            <div className="w-8 h-8 rounded-full animate-spin" style={{
              border: '3px solid var(--border-light)',
              borderTop: '3px solid var(--primary)',
              boxShadow: 'var(--shadow-sm)'
            }}></div>
            <div>
              <div className="text-xl font-semibold mb-1" style={{ color: 'var(--text-primary)' }}>Loading Messages</div>
              <div className="text-sm" style={{ color: 'var(--text-secondary)' }}>Connecting to secure messaging system...</div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen flex" style={{backgroundColor: 'var(--bg-primary)'}}>
      {/* Enhanced Conversations Sidebar */}
      <div className="w-1/3 flex flex-col" style={{ borderRight: '1px solid var(--border-light)', backgroundColor: 'var(--bg-primary)' }}>
        <div className="p-6" style={{ borderBottom: '1px solid var(--border-light)' }}>
          <h1 className="text-3xl font-light tracking-tight leading-tight mb-8" style={{
            color: 'var(--text-primary)',
            fontWeight: '200',
            letterSpacing: '-0.015em',
            textShadow: 'var(--shadow-text)'
          }}>Messages</h1>
          <div className="relative">
            <input
              type="text"
              placeholder="Search conversations and messages..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-12 pr-5 py-4 rounded-2xl focus:ring-0 focus:outline-none transition-all duration-300"
              style={{
                border: '2px solid var(--border-light)',
                backgroundColor: 'var(--bg-primary)',
                color: 'var(--text-primary)',
                boxShadow: 'var(--shadow-card)',
                fontSize: '16px',
                fontWeight: '400',
                transform: 'scale(1)',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'scale(1.02)'
                e.currentTarget.style.boxShadow = 'var(--shadow-hover)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'scale(1)'
                e.currentTarget.style.boxShadow = 'var(--shadow-card)'
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = 'var(--primary)'
                e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                e.currentTarget.style.transform = 'scale(1.02)'
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = 'var(--border-light)'
                e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                e.currentTarget.style.transform = 'scale(1)'
              }}
            />
            <svg
              className="absolute left-3 top-2.5 h-5 w-5"
              style={{ color: 'var(--text-muted)' }}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">
          {filteredConversations.map((conversation) => {
            const otherParticipant = conversation.participant_1_id === currentUser?.id 
              ? conversation.participant_2 
              : conversation.participant_1

            return (
              <div
                key={conversation.id}
                onClick={() => setSelectedConversation(conversation)}
                className="p-5 cursor-pointer transition-all duration-300 transform hover:scale-[1.02] group"
                style={{
                  borderBottom: '2px solid var(--border-light)',
                  backgroundColor: selectedConversation?.id === conversation.id ? 'var(--bg-accent)' : 'transparent',
                  borderRadius: '0.75rem',
                  margin: '0.5rem'
                }}
              >
                <div className="flex items-center space-x-4">
                  {otherParticipant?.avatar_url ? (
                    <img
                      src={otherParticipant.avatar_url}
                      alt=""
                      className="h-14 w-14 rounded-2xl object-cover shadow-lg ring-2 ring-accent group-hover:ring-primary transition-all duration-300"
                    />
                  ) : (
                    <div className="h-14 w-14 rounded-2xl flex items-center justify-center text-sm font-bold shadow-lg ring-2 ring-accent group-hover:ring-primary transition-all duration-300"
                         style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}>
                      {otherParticipant?.full_name
                        ? otherParticipant.full_name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
                        : otherParticipant?.email[0].toUpperCase() || '?'}
                    </div>
                  )}
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium truncate" style={{ color: 'var(--text-primary)' }}>
                        {otherParticipant?.full_name || otherParticipant?.email || 'Unknown User'}
                      </p>
                      {conversation.last_message && (
                        <p className="text-xs" style={{ color: 'var(--text-secondary)' }}>
                          {formatMessageTime(conversation.last_message.created_at)}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <p className="text-sm truncate" style={{ color: 'var(--text-secondary)' }}>
                        {conversation.last_message?.content || 'No messages yet'}
                      </p>
                      {(conversation.unread_count || 0) > 0 && (
                        <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full"
                              style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}>
                          {conversation.unread_count}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )
          })}

          {filteredConversations.length === 0 && (
            <div className="p-12 text-center">
              <div className="mx-auto h-16 w-16 rounded-2xl flex items-center justify-center mb-6 border" style={{
                backgroundColor: 'var(--bg-primary)',
                borderColor: 'var(--primary)',
                boxShadow: 'var(--shadow-light)'
              }}>
                <svg
                  className="h-8 w-8"
                  style={{ color: 'var(--primary)' }}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                {searchQuery ? 'No conversations found' : 'No conversations yet'}
              </h3>
              <p className="text-sm leading-relaxed max-w-sm mx-auto" style={{ color: 'var(--text-secondary)' }}>
                {searchQuery 
                  ? 'Try adjusting your search terms or check your message history.' 
                  : 'Start meaningful conversations with caregivers and families in your network.'}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 flex flex-col">
        {selectedConversation ? (
          <>
            {/* Chat Header */}
            <div className="p-4" style={{ backgroundColor: 'var(--bg-primary)', borderBottom: '1px solid var(--border-light)' }}>
              <div className="flex items-center space-x-3">
                {(() => {
                  const otherParticipant = selectedConversation.participant_1_id === currentUser?.id 
                    ? selectedConversation.participant_2 
                    : selectedConversation.participant_1

                  return (
                    <>
                      {otherParticipant?.avatar_url ? (
                        <img
                          src={otherParticipant.avatar_url}
                          alt=""
                          className="h-10 w-10 rounded-full object-cover"
                        />
                      ) : (
                        <div className="h-10 w-10 rounded-full flex items-center justify-center text-sm font-medium"
                             style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}>
                          {otherParticipant?.full_name
                            ? otherParticipant.full_name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
                            : otherParticipant?.email[0].toUpperCase() || '?'}
                        </div>
                      )}
                      <div>
                        <h2 className="text-lg font-semibold" style={{ color: 'var(--text-primary)' }}>
                          {otherParticipant?.full_name || otherParticipant?.email || 'Unknown User'}
                        </h2>
                        <p className="text-sm capitalize" style={{ color: 'var(--text-secondary)' }}>
                          {otherParticipant?.role?.replace('_', ' ') || 'User'}
                        </p>
                      </div>
                    </>
                  )
                })()}
              </div>
            </div>

            {/* Messages List */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messagesLoading ? (
                <div className="flex justify-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2" style={{ borderColor: 'var(--primary)' }}></div>
                </div>
              ) : (
                messages.map((message) => {
                  const isFromCurrentUser = message.sender_id === currentUser?.id

                  return (
                    <div
                      key={message.id}
                      className={`flex ${isFromCurrentUser ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-lg border"
                           style={{
                             backgroundColor: isFromCurrentUser ? 'var(--primary)' : 'var(--bg-primary)',
                             borderColor: isFromCurrentUser ? 'var(--primary)' : 'var(--border-light)',
                             color: isFromCurrentUser ? 'var(--bg-primary)' : 'var(--text-primary)'
                           }}>
                        <p className="text-sm">{message.content}</p>
                        <p className="text-xs mt-1"
                           style={{
                             color: isFromCurrentUser ? 'rgba(255, 255, 255, 0.8)' : 'var(--text-secondary)'
                           }}>
                          {formatMessageTime(message.created_at)}
                        </p>
                      </div>
                    </div>
                  )
                })
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Message Input */}
            <div className="p-4" style={{ backgroundColor: 'var(--bg-primary)', borderTop: '1px solid var(--border-light)' }}>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && !e.shiftKey && sendMessage()}
                  placeholder="Type your message..."
                  className="flex-1 px-4 py-2 rounded-lg focus:outline-none transition-all duration-200"
                  style={{
                    border: '1px solid var(--border-medium)',
                    backgroundColor: 'var(--bg-primary)',
                    color: 'var(--text-primary)',
                    boxShadow: 'var(--shadow-sm)'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-medium)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-sm)'
                  }}
                />
                <button
                  onClick={sendMessage}
                  disabled={!newMessage.trim()}
                  className="button-primary px-6 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors hover:opacity-90"
                >
                  Send
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center" style={{ backgroundColor: 'var(--bg-primary)' }}>
            <div className="text-center">
              <svg
                className="mx-auto h-12 w-12"
                style={{ color: 'var(--text-muted)' }}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium" style={{ color: 'var(--text-primary)' }}>Select a conversation</h3>
              <p className="mt-1 text-sm" style={{ color: 'var(--text-secondary)' }}>Choose a conversation from the sidebar to start messaging.</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default MessagingSystem
