import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Calendar, Clock, MapPin, Users, Plus, ArrowLeft, Search, CheckCircle, AlertCircle } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface CareGroupEvent {
  id: string
  title: string
  description: string
  event_date: string
  event_time: string
  location: string
  max_attendees?: number
  attendee_count: number
  event_type: 'meeting' | 'support' | 'social' | 'educational' | 'other'
  created_by: string
  tags: string[]
  attendees: Array<{
    user_id: string
    full_name: string
    status: 'attending' | 'maybe' | 'not_attending'
  }>
}

interface CareGroupInfo {
  id: string
  name: string
}

const CareGroupEventsPage: React.FC = () => {
  const { groupId } = useParams<{ groupId: string }>()
  const navigate = useNavigate()
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [careGroup, setCareGroup] = useState<CareGroupInfo | null>(null)
  const [events, setEvents] = useState<CareGroupEvent[]>([])
  const [userRole, setUserRole] = useState<string>('')
  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [newEvent, setNewEvent] = useState({
    title: '',
    description: '',
    event_date: '',
    event_time: '',
    location: '',
    max_attendees: '',
    event_type: 'meeting' as const
  })

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      if (user && groupId) {
        await loadGroupInfo()
        await loadEvents()
        await checkUserRole()
      }
      setLoading(false)
    }
    getUser()
  }, [groupId])

  const loadGroupInfo = async () => {
    if (!groupId) return

    try {
      const { data, error } = await supabase
        .from('care_groups')
        .select('id, name')
        .eq('id', groupId)
        .single()

      if (error) throw error
      setCareGroup(data)
    } catch (error) {
      console.error('Error loading group info:', error)
    }
  }

  const loadEvents = async () => {
    if (!groupId) return

    try {
      const { data: eventsData, error: eventsError } = await supabase
        .from('care_group_events')
        .select('id, title, description, event_date, event_time, location, max_attendees, attendee_count, event_type, created_by, tags')
        .eq('group_id', groupId)
        .order('event_date', { ascending: true })

      if (eventsError) throw eventsError

      const eventsWithAttendees = await Promise.all(
        (eventsData || []).map(async (event) => {
          const { data: attendeesData, error: attendeesError } = await supabase
            .from('care_group_event_attendees')
            .select(`
              user_id, status,
              profiles!care_group_event_attendees_user_id_fkey(full_name)
            `)
            .eq('event_id', event.id)

          if (attendeesError) {
            console.error('Error loading attendees for event:', event.id, attendeesError)
          }

          return {
            ...event,
            attendees: (attendeesData || []).map(attendee => ({
              user_id: attendee.user_id,
              full_name: attendee.profiles?.full_name || 'Unknown User',
              status: attendee.status
            }))
          }
        })
      )

      setEvents(eventsWithAttendees)
    } catch (error) {
      console.error('Error loading events:', error)
    }
  }

  const checkUserRole = async () => {
    if (!user || !groupId) return

    try {
      const { data, error } = await supabase
        .from('care_group_members')
        .select('role')
        .eq('group_id', groupId)
        .eq('user_id', user.id)
        .single()

      if (error) throw error
      setUserRole(data.role)
    } catch (error) {
      console.error('Error checking user role:', error)
    }
  }

  const createEvent = async () => {
    if (!user || !groupId || !newEvent.title || !newEvent.event_date) return

    setProcessing(true)
    try {
      const { data: eventData, error: eventError } = await supabase
        .from('care_group_events')
        .insert({
          group_id: groupId,
          title: newEvent.title,
          description: newEvent.description,
          event_date: newEvent.event_date,
          event_time: newEvent.event_time,
          location: newEvent.location,
          max_attendees: newEvent.max_attendees ? parseInt(newEvent.max_attendees) : null,
          event_type: newEvent.event_type,
          created_by: user.id,
          attendee_count: 1,
          tags: []
        })
        .select()
        .single()

      if (eventError) throw eventError

      await supabase
        .from('care_group_event_attendees')
        .insert({
          event_id: eventData.id,
          user_id: user.id,
          status: 'attending'
        })

      await supabase
        .from('care_group_activity')
        .insert({
          group_id: groupId,
          user_id: user.id,
          activity_type: 'event',
          content: `Created event: ${newEvent.title}`
        })

      await loadEvents()
      setShowCreateModal(false)
      setNewEvent({
        title: '',
        description: '',
        event_date: '',
        event_time: '',
        location: '',
        max_attendees: '',
        event_type: 'meeting'
      })
      alert('Event created successfully!')
    } catch (error) {
      console.error('Error creating event:', error)
      alert('Error creating event. Please try again.')
    } finally {
      setProcessing(false)
    }
  }

  const updateAttendance = async (eventId: string, status: 'attending' | 'maybe' | 'not_attending') => {
    if (!user) return

    setProcessing(true)
    try {
      const { error } = await supabase
        .from('care_group_event_attendees')
        .upsert({
          event_id: eventId,
          user_id: user.id,
          status: status
        })

      if (error) throw error

      const event = events.find(e => e.id === eventId)
      if (event) {
        const currentUserAttendance = event.attendees.find(a => a.user_id === user.id)
        let countChange = 0
        
        if (status === 'attending' && (!currentUserAttendance || currentUserAttendance.status !== 'attending')) {
          countChange = 1
        } else if (status !== 'attending' && currentUserAttendance?.status === 'attending') {
          countChange = -1
        }

        if (countChange !== 0) {
          await supabase
            .from('care_group_events')
            .update({ attendee_count: Math.max(0, event.attendee_count + countChange) })
            .eq('id', eventId)
        }
      }

      await loadEvents()
      alert('Attendance updated!')
    } catch (error) {
      console.error('Error updating attendance:', error)
      alert('Error updating attendance. Please try again.')
    } finally {
      setProcessing(false)
    }
  }

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'meeting': return 'bg-blue-100 text-blue-800'
      case 'support': return 'bg-purple-100 text-purple-800'
      case 'social': return 'bg-green-100 text-green-800'
      case 'educational': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getAttendanceStatus = (eventId: string) => {
    const event = events.find(e => e.id === eventId)
    const userAttendance = event?.attendees.find(a => a.user_id === user?.id)
    return userAttendance?.status || 'not_set'
  }

  const filteredEvents = events.filter(event =>
    event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    event.location.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const canManageEvents = userRole === 'admin' || userRole === 'moderator'

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-logo-green mx-auto mb-4"></div>
          <p className="text-gray-600">Loading events...</p>
        </div>
      </div>
    )
  }

  if (!careGroup) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Group Not Found</h1>
          <p className="text-gray-600 mb-6">The care group you're looking for doesn't exist.</p>
          <button
            onClick={() => navigate('/care-groups')}
            className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors"
          >
            Browse Groups
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen" style={{backgroundColor: 'var(--bg-primary)'}}>
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-6">
          <button
            onClick={() => navigate(`/care-groups/${groupId}`)}
            className="flex items-center gap-2 mb-4 transition-colors"
            style={{color: 'var(--text-secondary)'}}
            onMouseEnter={(e) => e.currentTarget.style.color = 'var(--text-primary)'}
            onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Group
          </button>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-full flex items-center justify-center" style={{backgroundColor: 'var(--primary)'}}>
                <Calendar className="w-6 h-6" style={{color: 'var(--bg-primary)'}} />
              </div>
              <div>
                <h1 className="text-2xl font-light" style={{color: 'var(--text-primary)'}}>Group Events</h1>
                <p style={{color: 'var(--text-secondary)'}}>{careGroup.name} • {filteredEvents.length} events</p>
              </div>
            </div>
            
            {canManageEvents && (
              <button
                onClick={() => setShowCreateModal(true)}
                className="px-5 py-2 rounded-lg transition-colors flex items-center gap-2"
                style={{backgroundColor: 'var(--primary)', color: 'var(--bg-primary)'}}
              >
                <Plus className="w-4 h-4" />
                Create Event
              </button>
            )}
          </div>
        </div>

        {/* Search */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search events..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-logo-green focus:border-logo-green"
            />
          </div>
        </div>

        {/* Events List */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredEvents.map((event) => (
            <div key={event.id} className="bg-white rounded-lg shadow-sm p-6">
              <div className="mb-4">
                <div className="flex items-center gap-3 mb-2">
                  <h3 className="text-lg font-semibold text-gray-900">{event.title}</h3>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getEventTypeColor(event.event_type)}`}>
                    {event.event_type}
                  </span>
                </div>
                
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    {new Date(event.event_date).toLocaleDateString()}
                    {event.event_time && (
                      <>
                        <Clock className="w-4 h-4 ml-2" />
                        {event.event_time}
                      </>
                    )}
                  </div>
                  {event.location && (
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4" />
                      {event.location}
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4" />
                    {event.attendee_count} attending
                    {event.max_attendees && ` (max ${event.max_attendees})`}
                  </div>
                </div>
                
                {event.description && (
                  <p className="mt-3 text-gray-700 text-sm">{event.description}</p>
                )}
              </div>
              
              {/* Attendance Controls */}
              <div className="border-t border-gray-200 pt-4">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600">
                    Status: <span className="font-medium">{getAttendanceStatus(event.id).replace('_', ' ')}</span>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => updateAttendance(event.id, 'attending')}
                      disabled={processing}
                      className={`px-3 py-1 text-xs rounded-full transition-colors ${
                        getAttendanceStatus(event.id) === 'attending'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-600 hover:bg-green-100 hover:text-green-800'
                      }`}
                    >
                      Attending
                    </button>
                    <button
                      onClick={() => updateAttendance(event.id, 'maybe')}
                      disabled={processing}
                      className={`px-3 py-1 text-xs rounded-full transition-colors ${
                        getAttendanceStatus(event.id) === 'maybe'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-gray-100 text-gray-600 hover:bg-yellow-100 hover:text-yellow-800'
                      }`}
                    >
                      Maybe
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredEvents.length === 0 && (
          <div className="text-center py-12">
            <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No events found</h3>
            <p className="text-gray-600 mb-6">
              {events.length === 0 
                ? "No events have been created yet." 
                : "Try adjusting your search criteria."
              }
            </p>
            {canManageEvents && events.length === 0 && (
              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors"
              >
                Create First Event
              </button>
            )}
          </div>
        )}

        {/* Create Event Modal */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-2xl w-full p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Create New Event</h3>
              
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Event Title *</label>
                    <input
                      type="text"
                      value={newEvent.title}
                      onChange={(e) => setNewEvent({ ...newEvent, title: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-logo-green focus:border-logo-green"
                      placeholder="Enter event title"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Event Type</label>
                    <select
                      value={newEvent.event_type}
                      onChange={(e) => setNewEvent({ ...newEvent, event_type: e.target.value as any })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-logo-green focus:border-logo-green"
                    >
                      <option value="meeting">Meeting</option>
                      <option value="support">Support</option>
                      <option value="social">Social</option>
                      <option value="educational">Educational</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                  <textarea
                    value={newEvent.description}
                    onChange={(e) => setNewEvent({ ...newEvent, description: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-logo-green focus:border-logo-green"
                    placeholder="Event description..."
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Date *</label>
                    <input
                      type="date"
                      value={newEvent.event_date}
                      onChange={(e) => setNewEvent({ ...newEvent, event_date: e.target.value })}
                      min={new Date().toISOString().split('T')[0]}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-logo-green focus:border-logo-green"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Time</label>
                    <input
                      type="time"
                      value={newEvent.event_time}
                      onChange={(e) => setNewEvent({ ...newEvent, event_time: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-logo-green focus:border-logo-green"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Max Attendees</label>
                    <input
                      type="number"
                      value={newEvent.max_attendees}
                      onChange={(e) => setNewEvent({ ...newEvent, max_attendees: e.target.value })}
                      min="1"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-logo-green focus:border-logo-green"
                      placeholder="Optional"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                  <input
                    type="text"
                    value={newEvent.location}
                    onChange={(e) => setNewEvent({ ...newEvent, location: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-logo-green focus:border-logo-green"
                    placeholder="Event location"
                  />
                </div>

                <div className="flex gap-3 pt-4">
                  <button
                    onClick={() => {
                      setShowCreateModal(false)
                      setNewEvent({
                        title: '',
                        description: '',
                        event_date: '',
                        event_time: '',
                        location: '',
                        max_attendees: '',
                        event_type: 'meeting'
                      })
                    }}
                    className="flex-1 bg-gray-100 text-gray-900 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={createEvent}
                    disabled={processing || !newEvent.title || !newEvent.event_date}
                    className="flex-1 bg-logo-green text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50"
                  >
                    {processing ? 'Creating...' : 'Create Event'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default CareGroupEventsPage
