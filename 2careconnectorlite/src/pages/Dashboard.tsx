import { useState, useEffect, useCallback, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { supabase } from '../lib/supabase'
import { dataService } from '../lib/dataService'
import { authService } from '../services/authService'
import { User, Calendar, MessageSquare, Heart, Bell, Settings, Menu, X, Home, Users, Shield, Pill, Activity } from 'lucide-react'

interface UserProfile {
  id: string
  email: string
  first_name?: string
  last_name?: string
  full_name?: string
  avatar_url?: string
  role?: string
  created_at?: string
}

export default function Dashboard() {
  const navigate = useNavigate()
  const [user, setUser] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [activityFilter, setActivityFilter] = useState('all')
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [dashboardStats, setDashboardStats] = useState({
    upcomingAppointments: 0,
    unreadMessages: 0,
    careGroupsCount: 0,
    savedProviders: 0,
    completedAppointments: 0,
    activeConversations: 0,
    healthGoalsProgress: 0,
    medicationReminders: 0
  })

  // Tab-specific data states
  const [appointments, setAppointments] = useState([])
  const [messages, setMessages] = useState([])
  const [careGroups, setCareGroups] = useState([])
  const [notifications, setNotifications] = useState([])
  const [tabDataLoading, setTabDataLoading] = useState(false)
  const [recentActivity, setRecentActivity] = useState<any[]>([])
  const [activityLoading, setActivityLoading] = useState(true)

  // Memoized dashboard stats for performance optimization
  const memoizedStats = useMemo(() => ({
    appointmentProgress: Math.min((dashboardStats.upcomingAppointments / 10) * 100, 100),
    messageProgress: Math.min((dashboardStats.unreadMessages / 20) * 100, 100),
    messagePriority: dashboardStats.unreadMessages > 5 ? 'high' : 'normal',
    careGroupProgress: Math.min((dashboardStats.careGroupsCount / 50) * 100, 100),
    healthProgress: Math.min((dashboardStats.healthGoalsProgress / 100) * 100, 100)
  }), [dashboardStats])



  // Optimized search handler
  const handleSearchChange = useCallback((query: string) => {
    setSearchQuery(query)
  }, [])

  // Optimized filter handler
  const handleFilterChange = useCallback((filter: string) => {
    setActivityFilter(filter)
  }, [])

  // Filtered activities based on search query and filter
  const filteredActivities = useMemo(() => {
    let filtered = recentActivity

    // Apply search query filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(activity => 
        activity.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        activity.description?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Apply activity filter (for future filter options)
    if (activityFilter !== 'all' && activityFilter !== 'All Activity') {
      filtered = filtered.filter(activity => activity.type === activityFilter)
    }

    return filtered
  }, [recentActivity, searchQuery, activityFilter])

  useEffect(() => {
    checkUser()
  }, [])

  const checkUser = async () => {
    try {
      console.log('🔐 Dashboard: Starting authentication check...')
      
      // First, check if user was just authenticated (from authService)
      const authenticatedUser = authService.getUser()
      if (authenticatedUser) {
        console.log('✅ Dashboard: Found authenticated user from authService:', authenticatedUser.email)
        setUser(authenticatedUser)
        // Load dashboard stats for the authenticated user
        await Promise.all([
          loadDashboardStats(authenticatedUser.id),
          loadRecentActivity(authenticatedUser.id)
        ])
        setLoading(false)
        return
      }
      
      // If no authenticated user in memory, check Supabase session
      console.log('🔍 Dashboard: No user in authService, checking Supabase session...')
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()
      
      if (sessionError) {
        console.error('❌ Dashboard: Session error:', sessionError)
        throw sessionError
      }
      
      if (session?.user) {
        console.log('✅ Dashboard: Found Supabase session for user:', session.user.email)
        
        // Try to get user profile with better error handling
        const currentUser = await authService.getCurrentUser()
        if (currentUser) {
          console.log('✅ Dashboard: Successfully loaded user profile:', currentUser.email)
          setUser(currentUser)
          // Load dashboard stats for the authenticated user
          await Promise.all([
            loadDashboardStats(currentUser.id),
            loadRecentActivity(currentUser.id)
          ])
        } else {
          console.warn('⚠️ Dashboard: Session exists but could not load user profile')
          // Don't redirect immediately - this might be a temporary issue
          setError('Loading user profile...')
          // Give a brief delay and try once more
          setTimeout(async () => {
            const retryUser = await authService.getCurrentUser()
            if (retryUser) {
              setUser(retryUser)
              setError('')
              await Promise.all([
                loadDashboardStats(retryUser.id),
                loadRecentActivity(retryUser.id)
              ])
            } else {
              console.log('❌ Dashboard: Profile load retry failed, redirecting to sign-in')
              setError('Please sign in to access your dashboard')
              navigate('/sign-in')
            }
            setLoading(false)
          }, 2000)
          return
        }
      } else {
        console.log('❌ Dashboard: No Supabase session found, redirecting to sign-in')
        setError('Please sign in to access your dashboard')
        navigate('/sign-in')
        return
      }
    } catch (error) {
      console.error('💥 Dashboard: Error during authentication check:', error)
      setError('Failed to load user data. Please try signing in again.')
      // Add a delay before redirecting to avoid immediate redirect loops
      setTimeout(() => {
        navigate('/sign-in')
      }, 1000)
    } finally {
      setLoading(false)
    }
  }

  const loadRecentActivity = async (userId: string) => {
    setActivityLoading(true);
    try {
      const { data, error } = await supabase
        .from('care_connector.bookings')
        .select('id, status, created_at')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) {
        throw error;
      }

      const formattedActivities = data?.map(booking => ({
        id: booking.id,
        type: 'Appointment',
        description: `Your appointment status was updated to ${booking.status}.`,
        timestamp: booking.created_at,
      })) || [];

      setRecentActivity(formattedActivities);
    } catch (error) {
      console.error('Error fetching recent activity:', error);
      // Set empty array on error so the UI doesn't break
      setRecentActivity([]);
    } finally {
      setActivityLoading(false);
    }
  };

  const loadDashboardStats = async (userId: string) => {
    try {
      // Get upcoming appointments count
      const { data: appointments } = await supabase
        .from('care_connector.bookings')
        .select('id')
        .eq('user_id', userId)
        .eq('status', 'confirmed')
        .gte('start_time', new Date().toISOString())

      // Get unread messages count from group_messages (messages in user's groups that they haven't read)
      const { data: messagesData } = await supabase
        .from('care_connector.group_messages')
        .select('id, group_id')
        .neq('user_id', userId)
        .not('read_by', 'cs', `{${userId}}`)

      const unreadMessages = messagesData?.length || 0

      // Get saved providers count from user_service_provider_favorites
      const { data: savedProvidersData } = await supabase
        .from('care_connector.user_service_provider_favorites')
        .select('id')
        .eq('user_id', userId)

      // Get care groups count
      const { data: careGroupsData } = await supabase
        .from('care_connector.care_group_members')
        .select('care_group_id')
        .eq('user_id', userId)

      // Get completed appointments count
      const { data: completedAppointments } = await supabase
        .from('care_connector.bookings')
        .select('id')
        .eq('user_id', userId)
        .eq('status', 'completed')

      setDashboardStats({
        upcomingAppointments: appointments?.length || 0,
        unreadMessages,
        careGroupsCount: careGroupsData?.length || 0,
        savedProviders: savedProvidersData?.length || 0,
        completedAppointments: completedAppointments?.length || 0,
        activeConversations: 0,
        healthGoalsProgress: 0,
        medicationReminders: 0
      })
    } catch (error) {
      console.error('Error loading dashboard stats:', error)
      setError('Failed to load dashboard statistics')
    }
  }

  // Load appointments data for appointments tab
  const loadAppointments = useCallback(async (userId: string) => {
    try {
      setTabDataLoading(true)
      const { data, error } = await supabase
        .from('care_connector.bookings')
        .select(`
          id,
          appointment_date,
          appointment_time,
          status,
          notes,
          provider_id,
          profiles!bookings_provider_id_fkey (
            first_name,
            last_name,
            role
          )
        `)
        .eq('user_id', userId)
        .order('appointment_date', { ascending: true })
        .limit(10)

      if (error) throw error
      setAppointments(data || [])
    } catch (error) {
      console.error('Error loading appointments:', error)
    } finally {
      setTabDataLoading(false)
    }
  }, [])

  // Load messages data for messages tab
  const loadMessages = useCallback(async (userId: string) => {
    try {
      setTabDataLoading(true)
      const { data, error } = await supabase
        .from('care_connector.group_messages')
        .select(`
          id,
          message,
          created_at,
          user_id,
          profiles!group_messages_user_id_fkey (
            first_name,
            last_name,
            full_name
          )
        `)
        .eq('recipient_id', userId)
        .order('created_at', { ascending: false })
        .limit(10)

      if (error) throw error
      setMessages(data || [])
    } catch (error) {
      console.error('Error loading messages:', error)
      setMessages([]) // Set empty array on error, no fake data
    } finally {
      setTabDataLoading(false)
    }
  }, [])

  // Load care groups data for care groups tab
  const loadCareGroups = useCallback(async (userId: string) => {
    try {
      setTabDataLoading(true)
      const { data, error } = await supabase
        .from('care_connector.care_group_members')
        .select(`
          id,
          joined_at,
          care_groups (
            id,
            name,
            description,
            member_count,
            privacy_setting
          )
        `)
        .eq('user_id', userId)
        .order('joined_at', { ascending: false })
        .limit(10)

      if (error) throw error
      setCareGroups(data || [])
    } catch (error) {
      console.error('Error loading care groups:', error)
    } finally {
      setTabDataLoading(false)
    }
  }, [])

  // Load notifications data for notifications tab with infinite loop protection
  const [notificationsLoading, setNotificationsLoading] = useState(false)
  const loadNotifications = useCallback(async (userId: string) => {
    // Prevent infinite loop - only allow one call at a time
    if (notificationsLoading) {
      console.log('🛑 Notifications already loading, preventing duplicate call')
      return
    }
    
    try {
      setNotificationsLoading(true)
      setTabDataLoading(true)
      console.log('🔄 Loading notifications for user:', userId)
      // Fetch real notifications from database
      const notificationsData = await dataService.getNotifications(userId)
      setNotifications(notificationsData || [])
      console.log('✅ Notifications loaded successfully:', notificationsData?.length || 0)
    } catch (error) {
      console.error('❌ Error loading notifications:', error)
      setNotifications([]) // Set empty array on error, no hardcoded data
    } finally {
      setNotificationsLoading(false)
      setTabDataLoading(false)
    }
  }, [notificationsLoading])

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut()
      navigate('/')
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  // Load tab-specific data when activeTab changes
  useEffect(() => {
    if (user?.id && activeTab !== 'overview') {
      switch (activeTab) {
        case 'appointments':
          loadAppointments(user.id)
          break
        case 'messages':
          loadMessages(user.id)
          break
        case 'care-groups':
          loadCareGroups(user.id)
          break
        case 'notifications':
          loadNotifications(user.id)
          break
        default:
          break
      }
    }
  }, [activeTab, user?.id])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center relative overflow-hidden" style={{backgroundColor: 'var(--bg-primary)'}}>
        {/* Background Elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 right-20 w-32 h-32 rounded-full animate-pulse-subtle" style={{backgroundColor: 'var(--bg-accent)'}}></div>
          <div className="absolute bottom-20 left-20 w-24 h-24 rounded-full animate-pulse-subtle" style={{backgroundColor: 'var(--bg-accent)', animationDelay: '1s'}}></div>
        </div>

        <div className="text-center max-w-md mx-auto p-8 relative z-10 animate-fadeInUp">
          <div className="w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg" style={{backgroundColor: 'var(--bg-accent)'}}>
            <div className="w-8 h-8 border-4 rounded-full animate-spin border-t-transparent" style={{borderColor: 'var(--primary)'}}></div>
          </div>
          <h2 className="text-2xl font-light mb-2" style={{color: 'var(--text-primary)'}}>Loading Dashboard</h2>
          <p style={{color: 'var(--text-secondary)'}}>Preparing your personalized care experience...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{backgroundColor: 'var(--bg-primary)'}}>
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" style={{backgroundColor: 'var(--bg-error)'}}>
            <div className="w-8 h-8 rounded-full" style={{backgroundColor: 'var(--text-error)'}}></div>
          </div>
          <h2 className="text-2xl font-bold tracking-tight leading-tight mb-3" style={{color: 'var(--text-primary)'}}>Connection Error</h2>
          <p className="text-base font-medium leading-relaxed mb-6" style={{color: 'var(--text-secondary)'}}>{error}</p>
          <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4 justify-center">
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-3 rounded-xl font-bold text-sm transition-all duration-200"
              style={{backgroundColor: 'var(--primary)', color: 'var(--bg-primary)'}}>
              Try Again
            </button>
            <button
              onClick={handleSignOut}
              className="px-6 py-3 rounded-xl font-bold text-sm border transition-all duration-200"
              style={{borderColor: 'var(--border-medium)', color: 'var(--text-primary)'}}
            >
              Sign Out
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col md:flex-row" style={{backgroundColor: 'var(--bg-primary)'}}>
      {/* Skip Link for Main Content */}
      <a href="#main-content" className="skip-link">
        Skip to main content
      </a>

      {/* Enhanced Mobile Header - Elegant & Clean */}
      <div
        className="md:hidden flex items-center justify-between p-6 border-b shadow-sm"
        style={{
          backgroundColor: 'var(--bg-primary)',
          borderColor: 'var(--border-light)'
        }}
      >
        <div className="flex items-center space-x-4">
          <div
            className="w-10 h-10 rounded-xl flex items-center justify-center shadow-sm"
            style={{ backgroundColor: 'var(--bg-accent)' }}
          >
            <Heart className="w-5 h-5" style={{ color: 'var(--primary)' }} />
          </div>
          <div>
            <h1 className="text-xl font-light" style={{ color: 'var(--text-primary)' }}>CareConnect</h1>
            <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>Dashboard</p>
          </div>
        </div>
        <button
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          className={`p-2 rounded-lg transition-colors ${mobileMenuOpen ? 'button-primary' : ''}`}
          style={{
            backgroundColor: mobileMenuOpen ? undefined : 'transparent'
          }}
          aria-label="Toggle navigation menu"
          aria-expanded={mobileMenuOpen}
          aria-controls="mobile-navigation-menu"
        >
          {mobileMenuOpen ? (
            <X className="w-6 h-6" style={{ color: 'var(--bg-primary)' }} />
          ) : (
            <Menu className="w-6 h-6" style={{ color: 'var(--primary)' }} />
          )}
        </button>
      </div>
      {/* Enhanced Apple Mac Desktop Style Sidebar */}
      <aside
        role="navigation"
        aria-label="Dashboard navigation"
        className={`
          w-64 flex-shrink-0 border-r transition-all duration-200 ease-in-out
          md:relative md:translate-x-0
          ${mobileMenuOpen ? 'fixed inset-y-0 left-0 z-50 translate-x-0' : 'fixed inset-y-0 left-0 z-50 -translate-x-full md:translate-x-0'}
        `}
        style={{
          backgroundColor: 'var(--bg-primary)',
          borderColor: 'var(--border-light)'
        }}
      >
        {/* Enhanced Sidebar Header */}


        {/* Enhanced Apple-Style Navigation Menu */}
        <nav className="px-8 py-6" role="navigation" aria-label="Dashboard navigation menu">
          <div className="space-y-3" role="tablist" aria-orientation="vertical">
            {/* Navigation Menu Items */}
            <button
              onClick={() => setActiveTab('overview')}
              role="tab"
              aria-selected={activeTab === 'overview'}
              aria-controls="overview-panel"
              id="overview-tab"
              tabIndex={activeTab === 'overview' ? 0 : -1}
              className={`w-full flex items-center gap-4 px-6 py-4 rounded-xl text-left transition-all duration-200 ${
                activeTab === 'overview'
                  ? 'shadow-lg'
                  : 'hover:shadow-md'
              }`}
              style={{
                backgroundColor: activeTab === 'overview' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'overview' ? 'var(--bg-primary)' : 'var(--text-secondary)',
                boxShadow: activeTab === 'overview' ? 'var(--shadow-medium)' : undefined
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  setActiveTab('overview');
                }
              }}
              onFocus={(e) => {
                if (activeTab !== 'overview') {
                  e.currentTarget.style.backgroundColor = 'var(--bg-accent)'
                  e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                }
              }}
              onBlur={(e) => {
                if (activeTab !== 'overview') {
                  e.currentTarget.style.backgroundColor = 'transparent'
                  e.currentTarget.style.boxShadow = 'none'
                }
              }}
            >
              <Home className="w-5 h-5" aria-hidden="true" />
              <span className="font-medium">Overview</span>
            </button>

            <button
              onClick={() => setActiveTab('appointments')}
              role="tab"
              aria-selected={activeTab === 'appointments'}
              aria-controls="appointments-panel"
              id="appointments-tab"
              tabIndex={activeTab === 'appointments' ? 0 : -1}
              className={`w-full flex items-center gap-4 px-6 py-4 rounded-xl text-left transition-all duration-200 ${
                activeTab === 'appointments'
                  ? 'shadow-lg'
                  : 'hover:shadow-md'
              }`}
              style={{
                backgroundColor: activeTab === 'appointments' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'appointments' ? 'var(--bg-primary)' : 'var(--text-secondary)',
                boxShadow: activeTab === 'appointments' ? 'var(--shadow-medium)' : undefined
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  setActiveTab('appointments');
                }
              }}
              onFocus={(e) => {
                if (activeTab !== 'appointments') {
                  e.currentTarget.style.backgroundColor = 'var(--bg-accent)'
                  e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                }
              }}
              onBlur={(e) => {
                if (activeTab !== 'appointments') {
                  e.currentTarget.style.backgroundColor = 'transparent'
                  e.currentTarget.style.boxShadow = 'none'
                }
              }}
            >
              <Calendar className="w-5 h-5" aria-hidden="true" />
              <span className="font-medium">Appointments</span>
            </button>

            <button
              onClick={() => setActiveTab('messages')}
              role="tab"
              aria-selected={activeTab === 'messages'}
              aria-controls="messages-panel"
              id="messages-tab"
              tabIndex={activeTab === 'messages' ? 0 : -1}
              className={`w-full flex items-center gap-4 px-6 py-4 rounded-xl text-left transition-all duration-200 ${
                activeTab === 'messages'
                  ? 'shadow-lg'
                  : 'hover:shadow-md'
              }`}
              style={{
                backgroundColor: activeTab === 'messages' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'messages' ? 'var(--bg-primary)' : 'var(--text-secondary)',
                boxShadow: activeTab === 'messages' ? 'var(--shadow-medium)' : undefined
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  setActiveTab('messages');
                }
              }}
            >
              <MessageSquare className="w-5 h-5" aria-hidden="true" />
              <span className="font-medium">Messages</span>
            </button>

            <button
              onClick={() => setActiveTab('care-groups')}
              role="tab"
              aria-selected={activeTab === 'care-groups'}
              aria-controls="care-groups-panel"
              id="care-groups-tab"
              tabIndex={activeTab === 'care-groups' ? 0 : -1}
              className={`w-full flex items-center gap-4 px-6 py-4 rounded-xl text-left transition-all duration-200 ${
                activeTab === 'care-groups'
                  ? 'shadow-lg'
                  : 'hover:shadow-md'
              }`}
              style={{
                backgroundColor: activeTab === 'care-groups' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'care-groups' ? 'var(--bg-primary)' : 'var(--text-secondary)',
                boxShadow: activeTab === 'care-groups' ? 'var(--shadow-medium)' : undefined
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  setActiveTab('care-groups');
                }
              }}
            >
              <Users className="w-5 h-5" aria-hidden="true" />
              <span className="font-medium">Care Groups</span>
            </button>

            <button
              onClick={() => setActiveTab('notifications')}
              role="tab"
              aria-selected={activeTab === 'notifications'}
              aria-controls="notifications-panel"
              id="notifications-tab"
              tabIndex={activeTab === 'notifications' ? 0 : -1}
              className={`w-full flex items-center gap-4 px-6 py-4 rounded-xl text-left transition-all duration-200 ${
                activeTab === 'notifications'
                  ? 'shadow-lg'
                  : 'hover:shadow-md'
              }`}
              style={{
                backgroundColor: activeTab === 'notifications' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'notifications' ? 'var(--bg-primary)' : 'var(--text-secondary)',
                boxShadow: activeTab === 'notifications' ? 'var(--shadow-medium)' : undefined
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  setActiveTab('notifications');
                }
              }}
            >
              <Bell className="w-5 h-5" aria-hidden="true" />
              <span className="font-medium">Notifications</span>
            </button>

            <button
              onClick={() => setActiveTab('providers')}
              role="tab"
              aria-selected={activeTab === 'providers'}
              aria-controls="providers-panel"
              id="providers-tab"
              tabIndex={activeTab === 'providers' ? 0 : -1}
              className={`w-full flex items-center gap-4 px-6 py-4 rounded-xl text-left transition-all duration-200 ${
                activeTab === 'providers'
                  ? 'shadow-lg'
                  : 'hover:shadow-md'
              }`}
              style={{
                backgroundColor: activeTab === 'providers' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'providers' ? 'var(--bg-primary)' : 'var(--text-secondary)',
                boxShadow: activeTab === 'providers' ? 'var(--shadow-medium)' : undefined
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  setActiveTab('providers');
                }
              }}
            >
              <User className="w-5 h-5" aria-hidden="true" />
              <span className="font-medium">Providers</span>
            </button>

            <button
              onClick={() => window.location.href = '/safety-location'}
              className="w-full flex items-center gap-4 px-6 py-4 rounded-xl text-left transition-all duration-300 hover:shadow-md"
              style={{
                backgroundColor: 'transparent',
                color: 'var(--text-secondary)'
              }}
            >
              <Shield className="w-5 h-5" aria-hidden="true" />
              <span className="font-medium">Safety & Location</span>
            </button>

            <button
              onClick={() => window.location.href = '/medication-management'}
              className="w-full flex items-center gap-4 px-6 py-4 rounded-xl text-left transition-all duration-300 hover:shadow-md"
              style={{
                backgroundColor: 'transparent',
                color: 'var(--text-secondary)'
              }}
            >
              <Pill className="w-5 h-5" aria-hidden="true" />
              <span className="font-medium">Medication Management</span>
            </button>

            <button
              onClick={() => setActiveTab('settings')}
              role="tab"
              aria-selected={activeTab === 'settings'}
              aria-controls="settings-panel"
              id="settings-tab"
              tabIndex={activeTab === 'settings' ? 0 : -1}
              className={`w-full flex items-center gap-4 px-6 py-4 rounded-xl text-left transition-all duration-200 ${
                activeTab === 'settings'
                  ? 'shadow-lg'
                  : 'hover:shadow-md'
              }`}
              style={{
                backgroundColor: activeTab === 'settings' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'settings' ? 'var(--bg-primary)' : 'var(--text-secondary)',
                boxShadow: activeTab === 'settings' ? 'var(--shadow-medium)' : undefined
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  setActiveTab('settings');
                }
              }}
            >
              <Settings className="w-5 h-5" aria-hidden="true" />
              <span className="font-medium">Settings</span>
            </button>

          </div>
        </nav>

        {/* User Profile Section */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t" style={{ borderColor: 'var(--border-light)' }}>
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold" style={{backgroundColor: 'var(--primary)', color: 'var(--bg-primary)'}}>
              {((user?.first_name?.[0] || '') + (user?.last_name?.[0] || '')).toUpperCase() || user?.email?.[0]?.toUpperCase() || 'U'}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-semibold truncate" style={{color: 'var(--text-primary)'}}>
                {user?.first_name || user?.email}
              </p>
              <button
                onClick={handleSignOut}
                className="text-xs font-medium transition-colors"
                style={{color: 'var(--text-secondary)'}}
                onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}>
                Sign out
              </button>
            </div>
          </div>
        </div>
      </aside>

      {/* Mobile Overlay */}
      {mobileMenuOpen && (
        <div
          className="fixed inset-0 z-40 md:hidden bg-overlay"
          onClick={() => setMobileMenuOpen(false)}
          aria-hidden="true"
        />
      )}

      {/* Main Content Area */}
      <main
        id="main-content"
        role="main"
        aria-label="Dashboard main content"
        className="flex-1 overflow-hidden md:ml-0"
        style={{ backgroundColor: 'var(--bg-content)' }}
      >
        <div className="h-full overflow-y-auto">
          {activeTab === 'overview' && (
            <div className="p-4 sm:p-6 lg:p-8 xl:p-10">
              {/* Header - Elegant & Clean */}
              <div className="mb-8 sm:mb-12 lg:mb-16">
                <h1 className="text-2xl sm:text-3xl lg:text-4xl font-light tracking-tight leading-tight mb-2" style={{
                  color: 'var(--text-primary)',
                  lineHeight: '1.2'
                }}>
                  Welcome back, {user?.first_name || 'there'}
                </h1>
                <p className="text-base sm:text-lg lg:text-xl font-normal leading-relaxed tracking-wide max-w-2xl" style={{
                  color: 'var(--text-secondary)',
                  lineHeight: '1.5'
                }}>
                  Here's what's happening with your care today.
                </p>
              </div>

              {/* Dashboard Overview Section - Clean */}
              <h2 className="text-lg sm:text-xl lg:text-2xl font-semibold tracking-tight leading-tight mb-6" style={{color: 'var(--text-primary)'}}>
                Overview
              </h2>

              {/* Dashboard Stats Grid - 2025 Mac/iOS Style */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8 mb-8 sm:mb-10 lg:mb-12">
                {/* Appointments Card - Enhanced Apple Style */}
                <div
                  className="p-6 rounded-2xl transition-all duration-300 cursor-pointer border hover:shadow-lg"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    boxShadow: 'var(--shadow-card)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)'
                    e.currentTarget.style.transform = 'translateY(-2px)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                    e.currentTarget.style.transform = 'translateY(0)'
                  }}
                >
                  <div className="flex items-center justify-between mb-4">
                    <Calendar className="w-6 h-6" style={{color: 'var(--primary)'}} />
                    <span className="text-2xl font-bold" style={{color: 'var(--text-primary)'}}>
                      {dashboardStats.upcomingAppointments}
                    </span>
                  </div>
                  <h3 className="text-lg font-semibold mb-2" style={{color: 'var(--text-primary)'}}>
                    Upcoming Appointments
                  </h3>
                  <div className="w-full rounded-full h-3 mb-2" style={{
                    backgroundColor: 'var(--border-light)',
                    boxShadow: 'var(--shadow-inset)'
                  }}>
                    <div
                      className="h-3 rounded-full transition-all duration-500"
                      style={{
                        width: `${memoizedStats.appointmentProgress}%`,
                        backgroundColor: 'var(--primary)',
                        boxShadow: 'var(--shadow-light)'
                      }}
                    ></div>
                  </div>
                  <button
                    onClick={() => setActiveTab('appointments')}
                    className="px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-md"
                    style={{
                      color: 'var(--bg-primary)',
                      backgroundColor: 'var(--primary)',
                      boxShadow: 'var(--shadow-card)'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                    }}>
                    {dashboardStats.upcomingAppointments === 0 ? 'Schedule Appointment' : 'Manage Appointments'}
                  </button>
                </div>

                {/* Messages Card - Enhanced Apple Style */}
                <div
                  className="p-6 rounded-2xl transition-all duration-300 cursor-pointer border hover:shadow-lg"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    boxShadow: 'var(--shadow-card)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)'
                    e.currentTarget.style.transform = 'translateY(-2px)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                    e.currentTarget.style.transform = 'translateY(0)'
                  }}
                >
                  <div className="flex items-center justify-between mb-4">
                    <MessageSquare className="w-6 h-6" style={{color: 'var(--primary)'}} />
                    <span className="text-2xl font-bold" style={{color: 'var(--text-primary)'}}>
                      {dashboardStats.unreadMessages}
                    </span>
                  </div>
                  <h3 className="text-lg font-semibold mb-2" style={{color: 'var(--text-primary)'}}>
                    Unread Messages
                  </h3>
                  <div className="w-full rounded-full h-3 mb-2" style={{
                    backgroundColor: 'var(--border-light)',
                    boxShadow: 'var(--shadow-inset)'
                  }}>
                    <div
                      className="h-3 rounded-full transition-all duration-500"
                      style={{
                        width: `${memoizedStats.messageProgress}%`,
                        backgroundColor: 'var(--primary)',
                        boxShadow: 'var(--shadow-light)'
                      }}
                    ></div>
                  </div>
                  <button
                    onClick={() => setActiveTab('messages')}
                    className="px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-md"
                    style={{
                      color: 'var(--bg-primary)',
                      backgroundColor: 'var(--primary)',
                      boxShadow: 'var(--shadow-card)'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                    }}>
                    {dashboardStats.unreadMessages === 0 ? 'Start Conversation' : 'Manage Messages'}
                  </button>
                </div>

                {/* Care Groups Card - Enhanced Apple Style */}
                <div
                  className="p-6 rounded-2xl transition-all duration-200 cursor-pointer border hover:shadow-lg"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    boxShadow: 'var(--shadow-light)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)'
                    e.currentTarget.style.transform = 'translateY(-2px)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-light)'
                    e.currentTarget.style.transform = 'translateY(0)'
                  }}
                >
                  <div className="flex items-center justify-between mb-4">
                    <Heart className="w-6 h-6" style={{color: 'var(--primary)'}} />
                    <span className="text-2xl font-bold" style={{color: 'var(--text-primary)'}}>
                      {dashboardStats.careGroupsCount}
                    </span>
                  </div>
                  <h3 className="text-lg font-bold mb-2" style={{color: 'var(--text-primary)'}}>
                    Care Groups
                  </h3>
                  <div className="w-full rounded-full h-3 mb-2" style={{ backgroundColor: 'var(--border-light)', boxShadow: 'inset 0 1px 2px rgba(0,0,0,0.1)' }}>
                    <div
                      className="h-3 rounded-full transition-all duration-500"
                      style={{ 
                        width: `${memoizedStats.careGroupProgress}%`,
                        backgroundColor: 'var(--primary)',
                        boxShadow: '0 1px 3px rgba(0,0,0,0.2)'
                      }}
                    ></div>
                  </div>
                  <button
                    onClick={() => setActiveTab('care-groups')}
                    className="px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-md"
                    style={{
                      color: 'var(--bg-primary)',
                      backgroundColor: 'var(--primary)',
                      boxShadow: 'var(--shadow-light)'
                    }}>
                    {dashboardStats.careGroupsCount === 0 ? 'Browse Groups' : 'Manage Groups'}
                  </button>
                </div>

                {/* Providers Card - Enhanced Apple Style */}
                <div
                  className="p-6 rounded-2xl transition-all duration-200 cursor-pointer border hover:shadow-lg"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    boxShadow: 'var(--shadow-light)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)'
                    e.currentTarget.style.transform = 'translateY(-2px)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-light)'
                    e.currentTarget.style.transform = 'translateY(0)'
                  }}
                >
                  <div className="flex items-center justify-between mb-4">
                    <User className="w-6 h-6" style={{color: 'var(--primary)'}} />
                    <span className="text-2xl font-bold" style={{color: 'var(--text-primary)'}}>
                      {dashboardStats.savedProviders}
                    </span>
                  </div>
                  <h3 className="text-lg font-bold mb-2" style={{color: 'var(--text-primary)'}}>
                    Saved Providers
                  </h3>
                  <div className="w-full rounded-full h-2 mb-2" style={{ backgroundColor: 'var(--border-light)' }}>
                    <div
                      className="h-2 rounded-full"
                      style={{ 
                        width: `${Math.min((dashboardStats.savedProviders / 10) * 100, 100)}%`,
                        backgroundColor: 'var(--primary)'
                      }}
                    ></div>
                  </div>
                  <button
                    onClick={() => setActiveTab('providers')}
                    className="px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-md"
                    style={{
                      color: 'var(--bg-primary)',
                      backgroundColor: 'var(--primary)',
                      boxShadow: 'var(--shadow-light)'
                    }}>
                    {dashboardStats.savedProviders === 0 ? 'Find Providers' : 'Manage Providers'}
                  </button>
                </div>
              </div>

              {/* Recent Activity Section */}
              <div className="mt-8 sm:mt-10 lg:mt-12">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8 sm:mb-10 space-y-4 sm:space-y-0">
                  <h2 className="text-xl sm:text-2xl font-semibold tracking-tight leading-tight" style={{ color: 'var(--text-primary)' }}>
                    Recent Activity
                  </h2>
                  <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
                    <div className="relative">
                      <input
                        type="text"
                        placeholder="Search activity..."
                        value={searchQuery}
                        onChange={(e) => handleSearchChange(e.target.value)}
                        className="w-full sm:w-72 pl-10 pr-4 py-3 rounded-xl text-sm transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:shadow-lg hover:shadow-md hover:scale-[1.02]"
                        style={{
                          backgroundColor: 'var(--bg-primary)',
                          border: '1px solid var(--border-light)',
                          color: 'var(--text-primary)',
                          boxShadow: 'var(--shadow-light)',
                          '--tw-ring-color': 'var(--primary)'
                        } as React.CSSProperties}
                      />
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg className="w-4 h-4" style={{ color: 'var(--text-muted)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      </div>
                    </div>
                    <select
                      value={activityFilter}
                      onChange={(e) => handleFilterChange(e.target.value)}
                      className="px-4 py-3 rounded-xl text-sm transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:shadow-lg hover:shadow-md hover:scale-[1.02]"
                      style={{
                        border: '1px solid var(--border-light)',
                        backgroundColor: 'var(--bg-primary)',
                        color: 'var(--text-primary)',
                        boxShadow: 'var(--shadow-light)',
                        '--tw-ring-color': 'var(--primary)'
                      } as React.CSSProperties}
                    >
                      <option value="all">All Activity</option>
                      <option value="appointments">Appointments</option>
                      <option value="messages">Messages</option>
                      <option value="care-groups">Care Groups</option>
                    </select>
                  </div>
                </div>

                {/* Activity Feed */}
                <div className="space-y-4">
                  {activityLoading ? (
                    <div className="text-center py-12">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto" style={{ borderColor: 'var(--primary)' }}></div>
                      <p className="mt-4" style={{ color: 'var(--text-secondary)' }}>Loading recent activity...</p>
                    </div>
                  ) : filteredActivities.length > 0 ? (
                    filteredActivities.map((activity) => {
                      const IconComponent = activity.icon; // Assuming icon is passed as a component
                      return (
                        <div key={activity.id} className="p-4 rounded-lg border" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                          <div className="flex items-start space-x-3">
                            <IconComponent className="w-5 h-5 mt-1" style={{ color: 'var(--primary)' }} />
                            <div className="flex-1">
                              <h3 className="font-semibold" style={{ color: 'var(--text-primary)' }}>
                                {activity.title}
                              </h3>
                              <p className="text-sm mt-1" style={{ color: 'var(--text-secondary)' }}>
                                {activity.description}
                              </p>
                              <p className="text-xs mt-2" style={{ color: 'var(--text-secondary)' }}>
                                {new Date(activity.date).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <div className="text-center py-16">
                      <Activity className="w-12 h-12 mx-auto mb-6" style={{ color: 'var(--text-muted)' }} />
                      <h3 className="text-xl sm:text-2xl font-semibold tracking-tight leading-tight mb-4" style={{ color: 'var(--text-primary)' }}>
                        Your Activity Story Starts Here
                      </h3>
                      <p className="text-sm font-normal leading-relaxed max-w-md mx-auto mb-6" style={{ color: 'var(--text-secondary)' }}>
                        Schedule appointments, send messages, or join care groups to see your activity timeline come to life.
                      </p>
                      <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
                        <button
                          onClick={() => setActiveTab('appointments')}
                          className="px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-md"
                          style={{
                            color: 'var(--bg-primary)',
                            backgroundColor: 'var(--primary)',
                            boxShadow: 'var(--shadow-light)'
                          }}>
                          Schedule Appointment
                        </button>
                        <button
                          onClick={() => setActiveTab('messages')}
                          className="px-4 py-2.5 text-sm font-semibold rounded-xl border transition-all duration-300 hover:scale-105 hover:shadow-md"
                          style={{
                            color: 'var(--text-primary)',
                            backgroundColor: 'transparent',
                            borderColor: 'var(--border-light)',
                            boxShadow: 'var(--shadow-light)'
                          }}>
                          Start Conversation
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'appointments' && (
            <div className="p-4 sm:p-6 md:p-8 lg:p-10 xl:p-12">
              <div className="mb-8">
                <h2 className="text-2xl font-light tracking-tight leading-tight mb-2" style={{color: 'var(--text-primary)', fontWeight: '300', letterSpacing: '-0.01em'}}>
                  Appointments
                </h2>
                <p className="text-lg font-normal leading-relaxed" style={{color: 'var(--text-secondary)', fontWeight: '400'}}>
                  Manage your upcoming and past appointments
                </p>
              </div>

              {tabDataLoading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto" style={{borderColor: 'var(--primary)'}}></div>
                  <p className="mt-4" style={{color: 'var(--text-secondary)'}}>Loading appointments...</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Appointment Management Actions */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <button
                        onClick={() => navigate('/book-appointment')}
                        className="px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-md"
                        style={{
                          color: 'var(--bg-primary)',
                          backgroundColor: 'var(--primary)',
                          boxShadow: 'var(--shadow-light)'
                        }}
                      >
                        Schedule New
                      </button>
                      <select
                        className="px-4 py-3 rounded-xl text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 hover:shadow-md"
                        style={{
                          border: '1px solid var(--border-light)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)',
                          boxShadow: 'var(--shadow-light)',
                          '--tw-ring-color': 'var(--primary)'
                        } as React.CSSProperties}
                      >
                        <option value="all">All Appointments</option>
                        <option value="upcoming">Upcoming</option>
                        <option value="past">Past</option>
                        <option value="pending">Pending</option>
                      </select>
                    </div>
                    <div className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                      {appointments.length} appointment{appointments.length !== 1 ? 's' : ''}
                    </div>
                  </div>

                  {/* Appointments List */}
                  <div className="space-y-4">
                    {appointments.length === 0 ? (
                      <div className="text-center py-12">
                        <Calendar className="w-12 h-12 mx-auto mb-6" style={{ color: 'var(--text-secondary)' }} />
                        <h3 className="text-base font-medium mb-2" style={{color: 'var(--text-primary)'}}>No Appointments Scheduled</h3>
                        <p className="mb-6" style={{color: 'var(--text-secondary)'}}>You don't have any appointments scheduled yet.</p>
                        <button
                          onClick={() => {
                            console.log('🔍 Find Care Providers button clicked - using React Router navigate')
                            navigate('/caregivers')
                          }}
                          className="px-4 py-2.5 text-sm font-semibold rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-md"
                          style={{
                            color: 'var(--bg-primary)',
                            backgroundColor: 'var(--primary)',
                            boxShadow: 'var(--shadow-light)'
                          }}
                        >
                          Find Care Providers
                        </button>
                      </div>
                    ) : appointments.map((appointment) => (
                      <div 
                        key={appointment.id} 
                        className="p-4 rounded-lg border transition-colors duration-150" 
                        style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }} 
                        onMouseEnter={(e) => {
                          e.currentTarget.style.borderColor = 'var(--primary)'
                          e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)'
                        }} 
                        onMouseLeave={(e) => {
                          e.currentTarget.style.borderColor = 'var(--border-light)'
                          e.currentTarget.style.boxShadow = 'none'
                        }}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-3 mb-2">
                              <h3 className="font-medium" style={{ color: 'var(--text-primary)' }}>
                                {appointment.profiles?.first_name} {appointment.profiles?.last_name}
                              </h3>
                              <span className="text-xs px-2 py-1 rounded" style={{ backgroundColor: 'var(--bg-primary)', color: 'var(--text-primary)' }}>
                                {appointment.type}
                              </span>
                            </div>
                            <p className="text-sm mb-1" style={{ color: 'var(--text-secondary)' }}>
                              {appointment.profiles?.role}
                            </p>
                            <div className="flex items-center space-x-4 text-sm" style={{ color: 'var(--text-secondary)' }}>
                              <span>📅 {new Date(appointment.appointment_date).toLocaleDateString()}</span>
                              <span>🕐 {appointment.appointment_time}</span>
                            </div>
                          </div>
                          <div className="flex items-center space-x-3">
                            <span
                              className={`px-3 py-1 rounded-full text-xs font-medium ${
                                appointment.status === 'confirmed' ? 'bg-success text-success-text' :
                                appointment.status === 'pending' ? 'bg-warning text-warning-text' :
                                'bg-neutral text-neutral-text'
                              }`}
                            >
                              {appointment.status}
                            </span>
                            <div className="flex items-center space-x-2">
                              <button className="text-xs px-2 py-1 rounded hover:opacity-80 transition-opacity" style={{ backgroundColor: 'var(--bg-primary)', color: 'var(--text-primary)' }}>
                                Reschedule
                              </button>
                              <button className="text-xs px-2 py-1 rounded hover:opacity-80 transition-opacity" style={{ backgroundColor: 'var(--bg-error)', color: 'var(--text-white)' }}>
                                Cancel
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'messages' && (
            <div className="p-4 sm:p-6 md:p-8 lg:p-10 xl:p-12">
              <div className="mb-6">
                <h2 className="text-xl sm:text-2xl font-semibold tracking-tight leading-tight" style={{color: 'var(--text-primary)'}}>
                  Messages
                </h2>
                <p className="text-sm leading-relaxed" style={{color: 'var(--text-secondary)'}}>
                  Communicate with your care team and providers
                </p>
              </div>

              {tabDataLoading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto" style={{borderColor: 'var(--primary)'}}></div>
                  <p className="mt-4" style={{color: 'var(--text-secondary)'}}>Loading messages...</p>
                </div>
              ) : (
                <div className="flex flex-col md:flex-row h-[calc(100vh-250px)]">
                  {/* Message List */}
                  <div className="w-full md:w-1/3 lg:w-2/5 xl:w-1/3 border-r border-light overflow-y-auto">
                    <div className="p-4">
                      <input
                        type="text"
                        placeholder="Search messages..."
                        className="w-full px-4 py-3 rounded-xl border text-sm min-w-0 transition-all duration-300 focus:ring-2 focus:ring-opacity-50 placeholder-styled"
                        style={{
                          minWidth: '200px',
                          borderColor: 'var(--border-light)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)',
                          boxShadow: 'var(--shadow-light)'
                        }}
                      />
                    </div>
                    <div className="divide-y divide-light">
                      {messages.length === 0 ? (
                        <div className="text-center py-12">
                          <MessageSquare className="w-12 h-12 mx-auto mb-6" style={{ color: 'var(--text-secondary)' }} />
                          <h3 className="text-lg font-semibold mb-2" style={{color: 'var(--text-primary)'}}>No Messages</h3>
                          <p className="mb-6" style={{color: 'var(--text-secondary)'}}>You don't have any messages yet.</p>
                          <button 
                            onClick={() => navigate('/messages')}
                            className="px-4 py-2.5 rounded-xl font-semibold text-sm transition-all duration-300 hover:scale-105 hover:shadow-md"
                            style={{
                              backgroundColor: 'var(--primary)',
                              color: 'var(--bg-primary)',
                              boxShadow: 'var(--shadow-light)'
                            }}
                          >
                            Start Conversation
                          </button>
                        </div>
                      ) : messages.map((message) => (
                        <div
                          key={message.id}
                          className={`p-4 cursor-pointer hover:bg-content ${
                            !message.read ? 'bg-secondary' : 'bg-primary'
                          }`}
                        >
                          <div className="flex justify-between items-start">
                            <h4 className="font-semibold text-sm" style={{color: !message.read ? 'var(--primary)' : 'var(--text-secondary)'}}>
                              {message.sender}
                            </h4>
                            <p className="text-sm leading-relaxed" style={{color: 'var(--text-secondary)'}}>
                              {new Date(message.timestamp).toLocaleDateString()}
                            </p>
                          </div>
                          <p className="text-sm truncate" style={{color: !message.read ? 'var(--primary)' : 'var(--text-secondary)'}}>
                            {message.subject}
                          </p>
                          <p className="text-xs text-secondary truncate">
                            {message.preview}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Message Detail */}
                  <div className="w-full md:w-2/3 lg:w-3/5 xl:w-2/3 p-4 sm:p-6 md:p-6 lg:p-8 overflow-y-auto">
                    {messages.length === 0 ? (
                      <div className="text-center py-20">
                        <MessageSquare className="w-12 h-12 mx-auto mb-6" style={{ color: 'var(--text-muted)' }} />
                        <h3 className="text-xl sm:text-2xl font-semibold tracking-tight leading-tight mb-3" style={{color: 'var(--primary)'}}>Select a Message</h3>
                        <p className="leading-relaxed" style={{color: 'var(--text-secondary)'}}>Choose a message from the left to view its contents here.</p>
                      </div>
                    ) : (
                      <div className="text-center py-20">
                        <MessageSquare className="w-12 h-12 mx-auto mb-6" style={{ color: 'var(--text-muted)' }} />
                        <h3 className="text-xl font-semibold mb-3" style={{color: 'var(--primary)'}}>Message System</h3>
                        <p className="leading-relaxed mb-6" style={{color: 'var(--text-secondary)'}}>The messaging system is ready for your conversations.</p>
                        <button 
                          onClick={() => navigate('/messages')}
                          className="button-primary px-6 py-3 rounded-lg font-medium"
                        >
                          Open Full Messaging
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'care-groups' && (
            <div className="p-4 sm:p-6 md:p-8 lg:p-10 xl:p-12">
              <div className="mb-6">
                <h2 className="text-lg font-medium" style={{color: 'var(--text-primary)'}}>
                  Care Groups
                </h2>
                <p className="text-sm" style={{color: 'var(--text-secondary)'}}>
                  Connect with others who share your health journey
                </p>
              </div>

              {tabDataLoading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto border-primary"></div>
                  <p className="mt-4 text-secondary">Loading care groups...</p>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <button className="button-primary px-4 py-2 rounded-lg font-medium">
                      Create New Group
                    </button>
                    <input
                      type="text"
                      placeholder="Search groups..."
                      className="w-72 px-4 py-3 rounded-xl text-sm transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:shadow-lg hover:shadow-md hover:scale-[1.02]"
                      style={{
                        backgroundColor: 'var(--bg-primary)',
                        border: '1px solid var(--border-light)',
                        color: 'var(--text-primary)',
                        boxShadow: 'var(--shadow-light)',
                        '--tw-ring-color': 'var(--primary)'
                      }}
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {careGroups.length === 0 ? (
                      <div className="col-span-full text-center py-12">
                        <Heart className="w-12 h-12 mx-auto mb-3" style={{color: 'var(--text-secondary)'}} />
                        <h3 className="text-base font-medium mb-2" style={{color: 'var(--text-primary)'}}>No Care Groups Joined</h3>
                        <p className="mb-6" style={{color: 'var(--text-secondary)'}}>You haven't joined any care groups yet.</p>
                        <button 
                          onClick={() => navigate('/care-groups')}
                          className="button-primary px-6 py-3 rounded-lg font-medium"
                        >
                          Browse Care Groups
                        </button>
                      </div>
                    ) : careGroups.map((membershipData, index) => {
                      const group = membershipData.care_groups
                      return (
                      <div key={index} className="p-5 rounded-lg border transition-colors duration-150" style={{backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)'}}>
                        <h3 className="font-medium text-base mb-2" style={{color: 'var(--text-primary)'}}>{group.name}</h3>
                        <p className="text-sm text-secondary mb-4">{group.description}</p>
                        <div className="flex items-center justify-between text-xs text-secondary">
                          <span>{group.member_count} members</span>
                          <span>{group.privacy_setting}</span>
                        </div>
                        <button className="mt-4 w-full button-secondary px-4 py-2 rounded-lg font-medium">
                          View Group
                        </button>
                      </div>
                    )
                    })}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'notifications' && (
            <div className="p-4 sm:p-6 md:p-8 lg:p-10 xl:p-12">
              <div className="mb-6">
                <h2 className="text-2xl font-bold tracking-tight" style={{color: 'var(--primary)'}}>
                  Notifications
                </h2>
                <p className="text-sm" style={{color: 'var(--text-secondary)'}}>
                  Stay updated on your health activities
                </p>
              </div>

              {tabDataLoading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto border-primary"></div>
                  <p className="mt-4 text-secondary">Loading notifications...</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {notifications.length > 0 ? (
                    notifications.map((notification) => (
                      <div key={notification.id} className="p-4 rounded-lg border border-light" style={{backgroundColor: 'var(--bg-primary)'}}>
                        <p className="text-sm" style={{color: 'var(--primary)'}}>{notification.title}</p>
                        <p className="text-xs mt-1" style={{color: 'var(--text-secondary)'}}>{notification.message}</p>
                        <p className="text-xs mt-1" style={{color: 'var(--text-secondary)'}}>
                          {new Date(notification.created_at).toLocaleDateString()}
                        </p>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-12">
                      <Bell className="w-16 h-16 mx-auto mb-4" style={{color: 'var(--text-secondary)'}} />
                      <h3 className="text-2xl font-bold tracking-tight" style={{color: 'var(--primary)'}}>
                        No New Notifications
                      </h3>
                      <p className="text-base font-medium leading-relaxed" style={{color: 'var(--text-secondary)'}}>
                        You're all caught up! Notifications will appear here
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="p-4 sm:p-6 md:p-8 lg:p-10 xl:p-12">
              <div className="mb-6">
                <h2 className="text-2xl font-bold tracking-tight" style={{color: 'var(--primary)'}}>
                  Settings
                </h2>
                <p className="text-sm" style={{color: 'var(--text-secondary)'}}>
                  Manage your account preferences and privacy settings
                </p>
              </div>

              {/* Settings Grid - Apple Mac System Preferences Style */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Profile Settings - Enhanced Apple Style */}
                <div className="p-6 rounded-2xl transition-all duration-200 cursor-pointer border hover:shadow-lg" style={{backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)', boxShadow: 'var(--shadow-light)'}}>
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-12 h-12 rounded-full flex items-center justify-center" style={{backgroundColor: 'var(--primary)'}}>
                      <User className="w-6 h-6" style={{color: 'var(--bg-primary)'}} />
                    </div>
                    <div>
                      <h3 className="font-semibold" style={{color: 'var(--text-primary)'}}>Profile</h3>
                      <p className="text-sm" style={{color: 'var(--text-secondary)'}}>Personal information</p>
                    </div>
                  </div>
                  <p className="text-sm leading-relaxed" style={{color: 'var(--text-secondary)'}}>
                    Update your name, email, and profile picture
                  </p>
                </div>

                {/* Notification Settings */}
                <div className="p-6 rounded-2xl transition-all duration-200 cursor-pointer border hover:shadow-lg" style={{backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)', boxShadow: 'var(--shadow-light)'}}>
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-12 h-12 rounded-full flex items-center justify-center" style={{backgroundColor: 'var(--primary)'}}>
                      <Bell className="w-6 h-6" style={{color: 'var(--bg-primary)'}} />
                    </div>
                    <div>
                      <h3 className="font-semibold" style={{color: 'var(--text-primary)'}}>Notifications</h3>
                      <p className="text-sm" style={{color: 'var(--text-secondary)'}}>Alert preferences</p>
                    </div>
                  </div>
                  <p className="text-sm leading-relaxed" style={{color: 'var(--text-secondary)'}}>
                    Manage email, push, and in-app notifications
                  </p>
                </div>

                {/* Privacy Settings */}
                <div className="p-6 rounded-2xl transition-all duration-200 cursor-pointer border hover:shadow-lg" style={{backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)', boxShadow: 'var(--shadow-light)'}}>
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-12 h-12 rounded-full flex items-center justify-center" style={{backgroundColor: 'var(--primary)'}}>
                      <Settings className="w-6 h-6" style={{color: 'var(--bg-primary)'}} />
                    </div>
                    <div>
                      <h3 className="font-semibold" style={{color: 'var(--text-primary)'}}>Privacy</h3>
                      <p className="text-sm" style={{color: 'var(--text-secondary)'}}>Data & security</p>
                    </div>
                  </div>
                  <p className="text-sm leading-relaxed" style={{color: 'var(--text-secondary)'}}>
                    Control who can see your information
                  </p>
                </div>

                {/* Care Preferences */}
                <div className="p-6 rounded-2xl transition-all duration-200 cursor-pointer border hover:shadow-lg" style={{backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)', boxShadow: 'var(--shadow-light)'}}>
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-12 h-12 rounded-full flex items-center justify-center" style={{backgroundColor: 'var(--primary)'}}>
                      <Heart className="w-6 h-6" style={{color: 'var(--bg-primary)'}} />
                    </div>
                    <div>
                      <h3 className="font-semibold" style={{color: 'var(--text-primary)'}}>Care Preferences</h3>
                      <p className="text-sm" style={{color: 'var(--text-secondary)'}}>Care settings</p>
                    </div>
                  </div>
                  <p className="text-sm leading-relaxed" style={{color: 'var(--text-secondary)'}}>
                    Set your care needs and preferences
                  </p>
                </div>

                {/* Account Settings */}
                <div className="p-6 rounded-2xl transition-all duration-200 cursor-pointer border hover:shadow-lg" style={{backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)', boxShadow: 'var(--shadow-light)'}}>
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-12 h-12 rounded-full flex items-center justify-center" style={{backgroundColor: 'var(--primary)'}}>
                      <User className="w-6 h-6" style={{color: 'var(--bg-primary)'}} />
                    </div>
                    <div>
                      <h3 className="font-semibold" style={{color: 'var(--text-primary)'}}>Account</h3>
                      <p className="text-sm" style={{color: 'var(--text-secondary)'}}>Security & billing</p>
                    </div>
                  </div>
                  <p className="text-sm leading-relaxed" style={{color: 'var(--text-secondary)'}}>
                    Password, billing, and account management
                  </p>
                </div>

                {/* Help & Support */}
                <div className="p-6 rounded-2xl transition-all duration-200 cursor-pointer border hover:shadow-lg" style={{backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)', boxShadow: 'var(--shadow-light)'}}>
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-12 h-12 rounded-full flex items-center justify-center" style={{backgroundColor: 'var(--primary)'}}>
                      <MessageSquare className="w-6 h-6" style={{color: 'var(--bg-primary)'}} />
                    </div>
                    <div>
                      <h3 className="font-semibold" style={{color: 'var(--text-primary)'}}>Help & Support</h3>
                      <p className="text-sm" style={{color: 'var(--text-secondary)'}}>Get assistance</p>
                    </div>
                  </div>
                  <p className="text-sm leading-relaxed" style={{color: 'var(--text-secondary)'}}>
                    Contact support and view help resources
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Providers Tab */}
          {activeTab === 'providers' && (
            <div className="p-4 sm:p-6 md:p-8 lg:p-10 xl:p-12">
              <div className="mb-6">
                <h2 className="text-lg font-medium" style={{color: 'var(--text-primary)'}}>
                  Providers
                </h2>
                <p className="text-sm" style={{color: 'var(--text-secondary)'}}>
                  Manage your saved healthcare providers and find new ones
                </p>
              </div>

              {tabDataLoading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto border-primary"></div>
                  <p className="mt-4 text-secondary">Loading providers...</p>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <button
                      onClick={() => {
                        console.log('🔍 Find New Providers button clicked')
                        window.location.href = '/find-care'
                      }}
                      className="button-primary px-4 py-2 rounded-lg font-medium">
                      Find New Providers
                    </button>
                    <input
                      type="text"
                      placeholder="Search providers..."
                      value={searchQuery}
                      onChange={(e) => handleSearchChange(e.target.value)}
                      className="w-72 px-4 py-3 rounded-xl text-sm transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:shadow-lg hover:shadow-md hover:scale-[1.02]"
                      style={{
                        backgroundColor: 'var(--bg-primary)',
                        border: '1px solid var(--border-light)',
                        color: 'var(--text-primary)',
                        boxShadow: 'var(--shadow-light)',
                        '--tw-ring-color': 'var(--primary)'
                      }}
                    />
                  </div>
                  
                  <div className="border rounded-lg p-6 text-center" style={{backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)'}}>
                    <User className="w-12 h-12 mx-auto mb-3" style={{color: 'var(--text-secondary)'}} />
                    <h3 className="text-base font-medium mb-2" style={{color: 'var(--text-primary)'}}>No Saved Providers</h3>
                    <p className="mb-4" style={{color: 'var(--text-secondary)'}}>
                      You haven't saved any healthcare providers yet. Find and save providers to easily book appointments.
                    </p>
                    <button
                      onClick={() => {
                        console.log('🔍 Browse Providers button clicked')
                        window.location.href = '/find-care'
                      }}
                      className="button-primary px-6 py-2 rounded-lg font-medium">
                      Browse Providers
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </main>
    </div>
  )
}




