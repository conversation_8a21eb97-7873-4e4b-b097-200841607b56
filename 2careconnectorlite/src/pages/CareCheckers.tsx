import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Search, Filter, MapPin, Clock, DollarSign, Star } from 'lucide-react'
import { dataService } from '../lib/dataService'
import { supabase } from '../lib/supabase'

interface CareChecker {
  id: string
  name: string
  bio: string
  location: string
  specialties: string[]
  verified: boolean
  provider_type: string
  hourly_rate?: number
  years_experience?: number
  profile_image?: string
  rating?: number
  reviews_count?: number
  availability_status?: string
}

export default function CareCheckers() {
  const navigate = useNavigate()
  const [careCheckers, setCareCheckers] = useState<CareChecker[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [location, setLocation] = useState('')
  const [availability, setAvailability] = useState('Any availability')
  const [maxRate, setMaxRate] = useState(200)
  const [minExperience, setMinExperience] = useState(0)
  const [searchError, setSearchError] = useState<string | null>(null)
  const [isSearching, setIsSearching] = useState(false)



  useEffect(() => {
    async function fetchCareCheckers() {
      try {
        setLoading(true)
        console.log('🔍 Fetching care checkers data...')
        // Fetch real care checkers data from database via dataService
        const data = await dataService.getCareCheckers()
        console.log('✅ Care checkers data received:', data)
        console.log('📊 Number of care checkers:', data?.length || 0)
        setCareCheckers(data || [])
      } catch (err) {
        console.error('❌ Error loading care checkers:', err)
        setCareCheckers([]) // Set empty array on error
      } finally {
        setLoading(false)
      }
    }
    fetchCareCheckers()
  }, [])

  // Auto-trigger search when filters change
  useEffect(() => {
    if (careCheckers.length > 0) {
      const timeoutId = setTimeout(() => {
        handleSearch()
      }, 500)

      return () => clearTimeout(timeoutId)
    }
  }, [searchTerm, location, availability, maxRate, minExperience, careCheckers.length])

  const handleSearch = async () => {
    setIsSearching(true)
    setSearchError(null)

    try {
      console.log('Searching with filters:', { searchTerm, location, availability, maxRate, minExperience })

      // Validate search inputs
      if (searchTerm.trim().length > 0 && searchTerm.trim().length < 2) {
        throw new Error('Search term must be at least 2 characters long')
      }

      if (maxRate < 0 || maxRate > 1000) {
        throw new Error('Maximum rate must be between $0 and $1000')
      }

      if (minExperience < 0 || minExperience > 50) {
        throw new Error('Experience must be between 0 and 50 years')
      }

      // Build search query with filters
      let query = supabase
        .schema('care_connector')
        .from('profiles')
        .select(`
          id,
          full_name,
          role,
          location,
          hourly_rate,
          rating,
          avatar_url,
          bio,
          specialties,
          availability_status,
          years_experience,
          phone,
          email
        `)
        .eq('role', 'care_checker')

      // Apply search term filter
      if (searchTerm.trim()) {
        query = query.or(`full_name.ilike.%${searchTerm.trim()}%,bio.ilike.%${searchTerm.trim()}%`)
      }

      // Apply location filter
      if (location.trim()) {
        query = query.ilike('location', `%${location.trim()}%`)
      }

      const { data, error: searchError } = await query

      if (searchError) {
        throw new Error(`Search failed: ${searchError.message}`)
      }

      // Transform data to match CareChecker interface
      const transformedData = (data || []).map((profile: any) => ({
        id: profile.id,
        name: profile.full_name || '',
        location: profile.location || '',
        hourly_rate: profile.hourly_rate || 0,
        rating: profile.rating || 0,
        avatar_url: profile.avatar_url || '',
        bio: profile.bio || '',
        specialties: profile.specialties || [],
        availability_status: profile.availability_status || 'available',
        years_experience: profile.years_experience || 0,
        phone: profile.phone || '',
        email: profile.email || ''
      }))

      setCareCheckers(transformedData)

    } catch (error: any) {
      console.error('Search error:', error)
      setSearchError(error.message || 'Failed to search care checkers')
    } finally {
      setIsSearching(false)
    }
  }

  const resetFilters = () => {
    setSearchTerm('')
    setLocation('')
    setAvailability('Any availability')
    setMaxRate(200)
    setMinExperience(0)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="text-center">
          <div className="inline-flex items-center gap-3 px-8 py-6 rounded-xl shadow-lg" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
            <div className="w-8 h-8 border-3 border-t-transparent rounded-full animate-spin" style={{ borderColor: 'var(--primary)' }}></div>
            <div>
              <div className="text-xl font-semibold mb-1" style={{ color: 'var(--text-primary)' }}>Loading Care Checkers</div>
              <div className="text-sm" style={{ color: 'var(--text-secondary)' }}>Finding quality assurance professionals for you...</div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Filter care checkers based on search criteria
  const filteredCareCheckers = careCheckers.filter(checker => {
    const matchesName = !searchTerm || checker.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesLocation = !location || checker.location.toLowerCase().includes(location.toLowerCase())
    const matchesRate = !checker.hourly_rate || checker.hourly_rate <= maxRate
    const matchesExperience = !checker.experience_years || checker.experience_years >= minExperience
    
    return matchesName && matchesLocation && matchesRate && matchesExperience
  })

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-secondary)' }}>
      {/* Header - Elegant & Clean */}
      <div className="page-header px-8 py-16" style={{ backgroundColor: 'var(--bg-primary)', borderBottom: '1px solid var(--border-light)' }}>
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl font-light mb-6" style={{ color: 'var(--text-primary)' }}>Find Care Checkers</h1>
          <p className="text-xl max-w-3xl mx-auto leading-relaxed" style={{ color: 'var(--text-secondary)' }}>
            {filteredCareCheckers.length} quality assurance professional{filteredCareCheckers.length !== 1 ? 's' : ''} available
          </p>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar Filters */}
        <div style={{
          width: '320px',
          backgroundColor: 'var(--bg-primary)',
          borderRight: '1px solid var(--border-light)',
          padding: '1.5rem'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '1.5rem' }}>
            <Filter style={{ width: '1.25rem', height: '1.25rem', color: 'var(--primary)' }} />
            <h2 style={{ fontSize: '1.125rem', fontWeight: '600', color: 'var(--text-primary)' }}>Search & Filters</h2>
          </div>

          {/* Error Display */}
          {searchError && (
            <div role="alert" style={{
              backgroundColor: 'var(--bg-error-light)',
              border: '1px solid var(--error)',
              color: 'var(--error)',
              padding: '0.75rem',
              borderRadius: '0.375rem',
              marginBottom: '1.5rem',
              fontSize: '0.875rem'
            }}>
              {searchError}
            </div>
          )}

          <form
            onSubmit={(e) => { e.preventDefault(); handleSearch(); }}
            role="search"
            aria-label="Search care checkers"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !isSearching) {
                e.preventDefault();
                handleSearch();
              }
            }}
          >
            {/* Search by name */}
          <div style={{ marginBottom: '1.5rem' }}>
            <label htmlFor="search-name" style={{
              display: 'block',
              fontSize: '0.875rem',
              fontWeight: '500',
              color: 'var(--text-primary)',
              marginBottom: '0.5rem'
            }}>
              Search by name
            </label>
            <div style={{ position: 'relative' }}>
              <Search style={{
                position: 'absolute',
                left: '0.75rem',
                top: '50%',
                transform: 'translateY(-50%)',
                width: '1rem',
                height: '1rem',
                color: 'var(--text-muted)'
              }} />
              <input
                id="search-name"
                type="text"
                placeholder="Search for care checkers by name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                aria-describedby={searchError ? "search-error" : undefined}
                style={{
                  width: '100%',
                  paddingLeft: '2.5rem',
                  paddingRight: '1rem',
                  paddingTop: '0.5rem',
                  paddingBottom: '0.5rem',
                  border: '1px solid var(--border-medium)',
                  borderRadius: '0.5rem',
                  backgroundColor: 'var(--bg-primary)',
                  fontSize: '1rem'
                }}
              />
            </div>
          </div>

          {/* Location */}
          <div style={{ marginBottom: '1.5rem' }}>
            <label htmlFor="location-input" style={{
              display: 'block',
              fontSize: '0.875rem',
              fontWeight: '500',
              color: 'var(--text-primary)',
              marginBottom: '0.5rem'
            }}>
              Location
            </label>
            <div style={{ position: 'relative' }}>
              <MapPin style={{
                position: 'absolute',
                left: '0.75rem',
                top: '50%',
                transform: 'translateY(-50%)',
                width: '1rem',
                height: '1rem',
                color: 'var(--text-muted)'
              }} />
              <input
                id="location-input"
                type="text"
                placeholder="Enter location..."
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                style={{
                  width: '100%',
                  paddingLeft: '2.5rem',
                  paddingRight: '1rem',
                  paddingTop: '0.5rem',
                  paddingBottom: '0.5rem',
                  border: '1px solid var(--border-medium)',
                  borderRadius: '0.5rem',
                  backgroundColor: 'var(--bg-primary)',
                  fontSize: '1rem'
                }}
              />
            </div>
          </div>

          {/* Availability */}
          <div style={{ marginBottom: '1.5rem' }}>
            <label htmlFor="availability-select" style={{
              display: 'block',
              fontSize: '0.875rem',
              fontWeight: '500',
              color: 'var(--text-primary)',
              marginBottom: '0.5rem'
            }}>
              Availability
            </label>
            <select
              id="availability-select"
              value={availability}
              onChange={(e) => setAvailability(e.target.value)}
              style={{
                width: '100%',
                padding: '0.5rem 0.75rem',
                border: '1px solid var(--border-medium)',
                borderRadius: '0.5rem',
                backgroundColor: 'var(--bg-primary)',
                fontSize: '1rem',
                color: 'var(--text-primary)'
              }}
            >
              <option>Any availability</option>
              <option>Available now</option>
              <option>Available within 24 hours</option>
              <option>Available within week</option>
            </select>
          </div>

          {/* Max Hourly Rate */}
          <div style={{ marginBottom: '1.5rem' }}>
            <label htmlFor="max-rate-slider" style={{
              display: 'block',
              fontSize: '0.875rem',
              fontWeight: '500',
              color: 'var(--text-primary)',
              marginBottom: '0.5rem'
            }}>
              Max Hourly Rate: ${maxRate}
            </label>
            <input
              id="max-rate-slider"
              type="range"
              min="20"
              max="200"
              value={maxRate}
              onChange={(e) => setMaxRate(parseInt(e.target.value))}
              style={{
                width: '100%',
                height: '0.5rem',
                borderRadius: '0.5rem',
                appearance: 'none',
                cursor: 'pointer',
                background: `linear-gradient(to right, var(--primary) 0%, var(--primary) ${((maxRate - 20) / 180) * 100}%, var(--border-light) ${((maxRate - 20) / 180) * 100}%, var(--border-light) 100%)`
              }}
            />
          </div>

          {/* Min Years Experience */}
          <div style={{ marginBottom: '1.5rem' }}>
            <label htmlFor="min-experience-slider" style={{
              display: 'block',
              fontSize: '0.875rem',
              fontWeight: '500',
              color: 'var(--text-primary)',
              marginBottom: '0.5rem'
            }}>
              Min Years Experience: {minExperience}
            </label>
            <input
              id="min-experience-slider"
              type="range"
              min="0"
              max="20"
              value={minExperience}
              onChange={(e) => setMinExperience(parseInt(e.target.value))}
              style={{
                width: '100%',
                height: '0.5rem',
                borderRadius: '0.5rem',
                appearance: 'none',
                cursor: 'pointer',
                background: `linear-gradient(to right, var(--primary) 0%, var(--primary) ${(minExperience / 20) * 100}%, var(--border-light) ${(minExperience / 20) * 100}%, var(--border-light) 100%)`
              }}
            />
          </div>

            {/* Search Button */}
            <button
              type="submit"
              disabled={isSearching}
              style={{
                width: '100%',
                backgroundColor: isSearching ? 'var(--text-muted)' : 'var(--primary)',
                color: 'var(--text-white)',
                padding: '0.5rem 1rem',
                borderRadius: '0.5rem',
                border: 'none',
                cursor: isSearching ? 'not-allowed' : 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '0.5rem',
                marginBottom: '1rem',
                fontSize: '1rem',
                fontWeight: '500'
              }}
            >
              {isSearching ? (
                <>
                  <div style={{
                    width: '1rem',
                    height: '1rem',
                    border: '2px solid transparent',
                    borderTop: '2px solid white',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }} />
                  Searching...
                </>
              ) : (
                <>
                  <Search style={{ width: '1rem', height: '1rem' }} />
                  Search
                </>
              )}
            </button>

            {/* ARIA Live Region for Screen Reader Announcements */}
            <div
              aria-live="polite"
              aria-atomic="true"
              style={{
                position: 'absolute',
                left: '-10000px',
                width: '1px',
                height: '1px',
                overflow: 'hidden'
              }}
            >
              {isSearching && "Searching for care checkers..."}
              {!isSearching && careCheckers.length > 0 && `Found ${careCheckers.length} care checkers`}
              {!isSearching && careCheckers.length === 0 && searchTerm && "No care checkers found for your search"}
            </div>
          </form>

          {/* Reset Filters */}
          <button
            onClick={resetFilters}
            className="w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            Reset Filters
          </button>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-8">
          {filteredCareCheckers.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <p className="text-gray-500 text-lg">No care checkers found matching your criteria.</p>
            </div>
          ) : (
            <div className="space-y-8">
              {filteredCareCheckers.map((checker) => (
                <div key={checker.id} className="group rounded-2xl p-8 transition-all duration-300 transform hover:scale-[1.01] hover:shadow-2xl animate-fadeInUp" style={{
                  backgroundColor: 'var(--bg-primary)',
                  border: '2px solid var(--border-light)',
                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
                }}>
                  <div className="flex items-start justify-between mb-6">
                    <div className="flex items-center gap-4">
                      <div className="w-16 h-16 rounded-2xl flex items-center justify-center shadow-lg ring-4 ring-accent group-hover:ring-primary transition-all duration-300" style={{ backgroundColor: 'var(--bg-accent)' }}>
                        <span className="font-bold text-lg" style={{ color: 'var(--primary)' }}>
                          {checker.initials || checker.name?.split(' ').map((n: string) => n[0]).join('') || 'C'}
                        </span>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold group-hover:text-primary transition-colors" style={{ color: 'var(--text-primary)' }}>{checker.full_name || `${checker.first_name || ''} ${checker.last_name || ''}`.trim()}</h3>
                        <div className="flex items-center gap-2">
                          {checker.is_verified && (
                            <span className="text-xs bg-emerald-100 text-emerald-700 px-2 py-1 rounded-full">Verified Provider</span>
                          )}
                          <span className="text-xs flex items-center gap-1" style={{ color: 'var(--primary)' }}>
                            <span className="w-2 h-2 rounded-full" style={{ backgroundColor: 'var(--primary)' }}></span>
                            Verified
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mb-4">
                    <p className="flex items-center gap-2 mb-2" style={{ color: 'var(--text-secondary)' }}>
                      <MapPin className="w-4 h-4" />
                      {checker.location}
                    </p>
                    {checker.needs && (
                      <p className="text-sm" style={{ color: 'var(--text-primary)' }}>
                        {checker.needs}
                      </p>
                    )}
                  </div>

                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <span className="font-semibold flex items-center gap-1" style={{ color: 'var(--primary)' }}>
                        <DollarSign className="w-4 h-4" />
                        ${checker.hourly_rate}/hour
                      </span>
                      {checker.years_of_experience && (
                        <span className="text-sm flex items-center gap-1" style={{ color: 'var(--text-secondary)' }}>
                          <Clock className="w-4 h-4" />
                          {checker.years_of_experience} years
                        </span>
                      )}
                    </div>
                  </div>

                  <button
                    onClick={() => navigate(`/provider/care-checker/${checker.id}`)}
                    className="button-primary"
                  >
                    View Profile
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <footer
        className="footer"
        style={{
          backgroundColor: 'var(--bg-primary)',
          borderTop: '1px solid var(--border-light)'
        }}
      >
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div
                className="w-8 h-8 rounded-lg flex items-center justify-center"
                style={{ backgroundColor: 'var(--primary)' }}
              >
                <span className="font-bold text-sm" style={{ color: 'var(--bg-primary)' }}>CC</span>
              </div>
              <div>
                <h3 className="text-lg font-semibold" style={{ color: 'var(--text-primary)' }}>Care Connector</h3>
                <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>Modern healthcare coordination.</p>
              </div>
            </div>
            <div className="flex items-center gap-8">
              <div>
                <h4 className="font-medium mb-2" style={{ color: 'var(--text-primary)' }}>Services</h4>
                <ul className="space-y-1 text-sm" style={{ color: 'var(--text-secondary)' }}>
                  <li>Find Care</li>
                  <li>Care Groups</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2" style={{ color: 'var(--text-primary)' }}>Learn More</h4>
                <ul className="space-y-1 text-sm" style={{ color: 'var(--text-secondary)' }}>
                  <li>How it Works</li>
                  <li>Features</li>
                </ul>
              </div>
            </div>
          </div>
          <div
            className="flex items-center justify-center gap-6 mt-6 pt-6"
            style={{ borderTop: '1px solid var(--border-light)' }}
          >
            <div className="flex items-center gap-2">
              <span
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: 'var(--primary)' }}
              ></span>
              <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>HIPAA Compliant</span>
            </div>
            <div className="flex items-center gap-2">
              <Star className="w-4 h-4" style={{ color: 'var(--primary)' }} />
              <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>Verified Providers</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
