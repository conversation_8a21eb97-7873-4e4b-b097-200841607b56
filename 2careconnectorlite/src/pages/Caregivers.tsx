import { useState, useEffect, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { Search, MapPin, Star, Clock, Users, MessageCircle, Shield, Heart } from 'lucide-react'
import { dataService } from '../lib/dataService'
import { supabase } from '../lib/supabase'

console.log('🔍 CAREGIVERS COMPONENT FILE LOADED - imports complete')

interface Caregiver {
  id: string
  full_name: string
  name?: string // For backward compatibility
  bio: string
  location: string
  specialties: string[]
  verified: boolean
  provider_type: string
  hourly_rate?: number
  years_experience?: number
  profile_image?: string
  avatar_url?: string // Database field name
  rating?: number
  average_rating?: number // Database field name
  reviews_count?: number
  availability_status?: string
}

export default function Caregivers() {
  const navigate = useNavigate()
  const [caregivers, setCaregivers] = useState<Caregiver[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchName, setSearchName] = useState('')
  const [searchLocation, setSearchLocation] = useState('')
  const [sortBy, setSortBy] = useState('name')
  const [maxRate, setMaxRate] = useState(200)
  const [selectedSpecialty, setSelectedSpecialty] = useState('')
  const [availabilityFilter, setAvailabilityFilter] = useState('')
  const [favorites, setFavorites] = useState<string[]>([])
  const [imageErrors, setImageErrors] = useState<string[]>([])
  const [searchLoading, setSearchLoading] = useState(false)
  const [userLocation, setUserLocation] = useState<{lat: number, lng: number} | null>(null)
  const [locationRadius, setLocationRadius] = useState(25) // miles
  const [gettingLocation, setGettingLocation] = useState(false)
  const [nearMeEnabled, setNearMeEnabled] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalResults, setTotalResults] = useState(0)
  const resultsPerPage = 20
  
  const toggleFavorite = (caregiverId: string) => {
    setFavorites(prev =>
      prev.includes(caregiverId)
        ? prev.filter(id => id !== caregiverId)
        : [...prev, caregiverId]
    )
  }

  const handleImageError = (caregiverId: string) => {
    setImageErrors(prev => [...prev, caregiverId])
  }

  const handleSearch = async () => {
    console.log('Search triggered with:', { searchName, searchLocation, selectedSpecialty, availabilityFilter })
    setSearchLoading(true)
    setError(null)

    try {
      // Enhanced validation for search inputs
      if (searchName.trim().length > 0 && searchName.trim().length < 2) {
        throw new Error('Search name must be at least 2 characters long')
      }

      if (searchName.trim().length > 50) {
        throw new Error('Search name must be less than 50 characters')
      }

      if (searchLocation.trim().length > 0 && searchLocation.trim().length < 2) {
        throw new Error('Location must be at least 2 characters long')
      }

      if (searchLocation.trim().length > 100) {
        throw new Error('Location must be less than 100 characters')
      }

      if (maxRate < 0 || maxRate > 1000) {
        throw new Error('Maximum rate must be between $0 and $1000')
      }

      // Validate that at least one search criteria is provided
      if (!searchName.trim() && !searchLocation.trim() && !selectedSpecialty && !availabilityFilter) {
        throw new Error('Please provide at least one search criteria (name, location, specialty, or availability)')
      }
      // Build search query with filters
      let query = supabase
        .schema('care_connector')
        .from('profiles')
        .select(`
          id,
          full_name,
          role,
          location,
          hourly_rate,
          rating,
          avatar_url,
          bio,
          specialties,
          availability_status,
          years_experience,
          phone,
          email
        `)
        .eq('role', 'caregiver')

      // Apply name search filter
      if (searchName.trim()) {
        query = query.or(`full_name.ilike.%${searchName.trim()}%,bio.ilike.%${searchName.trim()}%`)
      }

      // Apply location filter
      if (searchLocation.trim()) {
        query = query.ilike('location', `%${searchLocation.trim()}%`)
      }

      // Apply specialty filter
      if (selectedSpecialty) {
        query = query.contains('specialties', [selectedSpecialty])
      }

      // Apply availability filter
      if (availabilityFilter) {
        query = query.eq('availability_status', availabilityFilter)
      }

      // Apply rate filter
      if (maxRate < 200) {
        query = query.lte('hourly_rate', maxRate)
      }

      // Get total count with the same filters applied
      let countQuery = supabase
        .schema('care_connector')
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .eq('role', 'caregiver')

      // Apply same filters to count query for accurate pagination
      if (searchName.trim()) {
        countQuery = countQuery.or(`full_name.ilike.%${searchName.trim()}%,bio.ilike.%${searchName.trim()}%`)
      }
      if (searchLocation.trim()) {
        countQuery = countQuery.ilike('location', `%${searchLocation.trim()}%`)
      }
      if (selectedSpecialty) {
        countQuery = countQuery.contains('specialties', [selectedSpecialty])
      }
      if (availabilityFilter) {
        countQuery = countQuery.eq('availability_status', availabilityFilter)
      }
      if (maxRate < 200) {
        countQuery = countQuery.lte('hourly_rate', maxRate)
      }

      const { count } = await countQuery
      setTotalResults(count || 0)

      // Get paginated results
      const offset = (currentPage - 1) * resultsPerPage
      const { data, error: searchError } = await query
        .range(offset, offset + resultsPerPage - 1)

      if (searchError) {
        throw new Error(`Search failed: ${searchError.message}`)
      }

      // Transform data to match Caregiver interface
      let transformedData = (data || []).map(profile => ({
        id: profile.id,
        name: profile.full_name || '',
        full_name: profile.full_name || '',
        location: profile.location || '',
        hourly_rate: profile.hourly_rate || 0,
        rating: profile.rating || 0,
        avatar_url: profile.avatar_url || '',
        profile_image: profile.avatar_url || '',
        bio: profile.bio || '',
        specialties: profile.specialties || [],
        availability_status: profile.availability_status || 'available',
        years_experience: profile.years_experience || 0,
        phone: profile.phone || '',
        email: profile.email || '',
        verified: true,
        provider_type: 'caregiver' as const
      }))

      // Apply location-based filtering if "Near Me" is enabled
      if (nearMeEnabled && userLocation) {
        transformedData = transformedData.filter((caregiver) => {
          // Parse caregiver location to get approximate coordinates
          // This is a simplified geocoding approach - in production you'd use a proper geocoding service
          const caregiverCoords = getCaregiverLocationCoords(caregiver.location)

          if (!caregiverCoords) {
            // If we can't determine location, include in results but with lower priority
            return true
          }

          const distance = calculateDistance(
            userLocation.lat,
            userLocation.lng,
            caregiverCoords.lat,
            caregiverCoords.lng
          )
          return distance <= locationRadius
        })
      }

      setCaregivers(transformedData)

      // Track search analytics
      console.log('🔍 Search Analytics:', {
        searchTerm: searchName,
        location: searchLocation,
        specialty: selectedSpecialty,
        availability: availabilityFilter,
        maxRate,
        resultsCount: transformedData.length,
        timestamp: new Date().toISOString()
      })
    } catch (err) {
      console.error('Search error:', err)
      setError(err instanceof Error ? err.message : 'Search failed')
    } finally {
      setSearchLoading(false)
    }
  }

  const getCurrentLocation = () => {
    setGettingLocation(true)

    if (!navigator.geolocation) {
      alert('Geolocation is not supported by this browser')
      setGettingLocation(false)
      return
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords
        setUserLocation({ lat: latitude, lng: longitude })
        setNearMeEnabled(true)
        setSearchLocation(`Near me (${latitude.toFixed(4)}, ${longitude.toFixed(4)})`)
        setGettingLocation(false)
      },
      (error) => {
        console.error('Location error:', error)
        alert(`Location error: ${error.message}`)
        setGettingLocation(false)
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000
      }
    )
  }

  // Calculate distance between two coordinates (used for location-based search)
  const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
    const R = 3959 // Earth's radius in miles
    const dLat = (lat2 - lat1) * Math.PI / 180
    const dLng = (lng2 - lng1) * Math.PI / 180
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return R * c
  }

  // Get approximate coordinates for caregiver location
  const getCaregiverLocationCoords = (location: string): {lat: number, lng: number} | null => {
    // Simplified location mapping - in production, use a proper geocoding service
    const locationMap: {[key: string]: {lat: number, lng: number}} = {
      'San Francisco, CA': { lat: 37.7749, lng: -122.4194 },
      'Los Angeles, CA': { lat: 34.0522, lng: -118.2437 },
      'New York, NY': { lat: 40.7128, lng: -74.0060 },
      'Chicago, IL': { lat: 41.8781, lng: -87.6298 },
      'Houston, TX': { lat: 29.7604, lng: -95.3698 },
      'Phoenix, AZ': { lat: 33.4484, lng: -112.0740 },
      'Philadelphia, PA': { lat: 39.9526, lng: -75.1652 },
      'San Antonio, TX': { lat: 29.4241, lng: -98.4936 },
      'San Diego, CA': { lat: 32.7157, lng: -117.1611 },
      'Dallas, TX': { lat: 32.7767, lng: -96.7970 }
    }

    // Try exact match first
    if (locationMap[location]) {
      return locationMap[location]
    }

    // Try partial matches for cities
    for (const [key, coords] of Object.entries(locationMap)) {
      if (location.toLowerCase().includes(key.toLowerCase().split(',')[0])) {
        return coords
      }
    }

    return null
  }

  // Fetch caregivers data
  useEffect(() => {
    console.log('🔍 CAREGIVERS COMPONENT MOUNTED - useEffect triggered')

    const fetchCaregivers = async () => {
      try {
        setLoading(true)
        // Fetch from database with proper error handling
        const { data, error } = await supabase
          .schema('care_connector')
          .from('profiles')
          .select('*')
          .eq('role', 'caregiver')
          .limit(20)
        
        if (error) {
          console.error('❌ Error fetching caregivers:', error)
          setError('Failed to load caregivers. Please try again.')
        } else {
          console.log('✅ Caregivers fetched successfully:', data?.length || 0)
          setCaregivers(data || [])
        }
      } catch (error) {
        console.error('❌ Error fetching caregivers:', error)
        setError('Failed to load caregivers. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    fetchCaregivers()
  }, [])

  // Auto-trigger search when filters change - TEMPORARILY DISABLED TO FIX INFINITE LOOP
  // useEffect(() => {
  //   if (caregivers.length > 0) { // Only search if we have initial data
  //     const timeoutId = setTimeout(() => {
  //       handleSearch()
  //     }, 500) // Debounce search by 500ms

  //     return () => clearTimeout(timeoutId)
  //   }
  // }, [searchName, searchLocation, selectedSpecialty, availabilityFilter, maxRate, currentPage])

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="text-center">
          <div className="inline-flex items-center gap-3 px-8 py-6 rounded-xl shadow-lg" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
            <div className="w-8 h-8 border-3 border-t-transparent rounded-full animate-spin" style={{ borderColor: 'var(--primary)' }}></div>
            <span className="text-lg font-medium" style={{ color: 'var(--text-primary)' }}>Loading caregivers...</span>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="text-center max-w-md mx-auto p-6">
          <div className="bg-white rounded-xl shadow-lg p-8" style={{ border: '1px solid var(--border-light)' }}>
            <div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6" style={{backgroundColor: 'var(--bg-error-light)'}}>
              <svg className="w-8 h-8" style={{color: 'var(--text-error)'}} fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-3" style={{color: 'var(--text-primary)'}}>
              Unable to Load Caregivers
            </h3>
            <p className="text-base mb-6" style={{color: 'var(--text-secondary)'}}>
              {error}
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button
                onClick={() => {
                  setError(null)
                  setLoading(true)
                  // Retry loading caregivers
                  window.location.reload()
                }}
                className="px-6 py-3 rounded-lg font-medium transition-all duration-200"
                style={{
                  backgroundColor: 'var(--primary)',
                  color: 'white'
                }}
                aria-label="Retry loading caregivers"
              >
                Try Again
              </button>
              <button
                onClick={() => {
                  setError(null)
                  setCaregivers([])
                }}
                className="px-6 py-3 rounded-lg font-medium transition-all duration-200"
                style={{
                  backgroundColor: 'var(--bg-secondary)',
                  color: 'var(--text-primary)',
                  border: '1px solid var(--border-medium)'
                }}
                aria-label="Continue without loading caregivers"
              >
                Continue Anyway
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Use database search results directly with sorting only
  const filteredCaregivers = useMemo(() => {
    return caregivers.sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return (b.average_rating || b.rating || 0) - (a.average_rating || a.rating || 0)
        case 'experience':
          return (b.years_experience || 0) - (a.years_experience || 0)
        case 'rate':
          return (a.hourly_rate || 0) - (b.hourly_rate || 0)
        default:
          const aName = a.full_name || a.name || ''
          const bName = b.full_name || b.name || ''
          return aName.localeCompare(bName)
      }
    })
  }, [caregivers, sortBy])

  return (
    <main className="min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
      {/* Page Header */}
      <div className="pt-8 pb-12 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-5xl font-light tracking-tight leading-tight mb-6" style={{
              color: 'var(--text-primary)',
              fontWeight: '300',
              letterSpacing: '-0.025em'
            }}>
              Find Care Providers
            </h1>
            <p className="text-xl font-normal leading-relaxed max-w-3xl mx-auto" style={{
              color: 'var(--text-secondary)',
              fontWeight: '400',
              lineHeight: '1.7'
            }}>
              Discover qualified caregivers in your area who can provide the personalized care you need
            </p>
          </div>

          {/* Search Filters */}
          <div className="bg-white rounded-2xl p-8 shadow-lg mb-12" style={{
            border: '1px solid var(--border-light)',
            boxShadow: 'var(--shadow-card)'
          }}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6">
              <div>
                <label className="block text-sm font-semibold mb-2" style={{color: 'var(--text-primary)'}}>
                  Search by Name
                </label>
                <input
                  type="text"
                  value={searchName}
                  onChange={(e) => setSearchName(e.target.value)}
                  placeholder="Enter caregiver name"
                  className="w-full px-4 py-3 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:border-transparent"
                  style={{
                    borderColor: 'var(--border-light)',
                    backgroundColor: 'var(--bg-primary)',
                    color: 'var(--text-primary)',
                    '--tw-ring-color': 'var(--primary)',
                    '--tw-ring-opacity': '0.2'
                  } as React.CSSProperties}
                  aria-label="Search caregivers by name"
                />
              </div>
              
              <div>
                <label className="block text-sm font-semibold mb-2" style={{color: 'var(--text-primary)'}}>
                  Location
                </label>
                <input
                  type="text"
                  value={searchLocation}
                  onChange={(e) => setSearchLocation(e.target.value)}
                  placeholder="City or ZIP code"
                  className="w-full px-4 py-3 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:border-transparent"
                  style={{
                    borderColor: 'var(--border-light)',
                    backgroundColor: 'var(--bg-primary)',
                    color: 'var(--text-primary)',
                    '--tw-ring-color': 'var(--primary)',
                    '--tw-ring-opacity': '0.2'
                  } as React.CSSProperties}
                  aria-label="Search caregivers by location"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                  Specialty
                </label>
                <select
                  value={selectedSpecialty}
                  onChange={(e) => setSelectedSpecialty(e.target.value)}
                  className="w-full px-4 py-3 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:border-transparent"
                  style={{
                    borderColor: 'var(--border-light)',
                    backgroundColor: 'var(--bg-primary)',
                    color: 'var(--text-primary)',
                    '--tw-ring-color': 'var(--primary)',
                    '--tw-ring-opacity': '0.2'
                  } as React.CSSProperties}
                >
                  <option value="">All Specialties</option>
                  <option value="elderly-care">Elderly Care</option>
                  <option value="disability-support">Disability Support</option>
                  <option value="child-care">Child Care</option>
                  <option value="medical-care">Medical Care</option>
                  <option value="companion-care">Companion Care</option>
                  <option value="respite-care">Respite Care</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                  Sort By
                </label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full px-4 py-3 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:border-transparent"
                  style={{
                    borderColor: 'var(--border-light)',
                    backgroundColor: 'var(--bg-primary)',
                    color: 'var(--text-primary)',
                    '--tw-ring-color': 'var(--primary)',
                    '--tw-ring-opacity': '0.2'
                  } as React.CSSProperties}
                >
                  <option value="name">Name</option>
                  <option value="rating">Rating</option>
                  <option value="experience">Experience</option>
                  <option value="rate">Hourly Rate</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                  Max Rate ($/hr)
                </label>
                <input
                  type="number"
                  value={maxRate}
                  onChange={(e) => setMaxRate(Number(e.target.value) || 0)}
                  placeholder="200"
                  min="0"
                  max="1000"
                  className="w-full px-4 py-3 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:border-transparent"
                  style={{
                    borderColor: 'var(--border-light)',
                    backgroundColor: 'var(--bg-primary)',
                    color: 'var(--text-primary)',
                    '--tw-ring-color': 'var(--primary)',
                    '--tw-ring-opacity': '0.2'
                  } as React.CSSProperties}
                />
              </div>

              <div className="flex items-end gap-4">
                <button
                  onClick={handleSearch}
                  disabled={searchLoading}
                  className="flex-1 px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:shadow-lg hover:scale-105 flex items-center justify-center gap-3 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
                  style={{
                    backgroundColor: searchLoading ? 'var(--text-muted)' : 'var(--primary)',
                    color: 'var(--bg-primary)',
                    boxShadow: 'var(--shadow-medium)',
                    fontSize: '16px',
                    fontWeight: '600',
                    letterSpacing: '0.025em'
                  }}
                  onMouseEnter={(e) => {
                    if (!searchLoading) {
                      e.currentTarget.style.backgroundColor = 'var(--primary-hover)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)'
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!searchLoading) {
                      e.currentTarget.style.backgroundColor = 'var(--primary)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
                    }
                  }}
                >
                  {searchLoading ? (
                    <div className="w-5 h-5 border-2 border-t-transparent rounded-full animate-spin" style={{ borderColor: 'var(--bg-primary)' }}></div>
                  ) : (
                    <Search className="w-5 h-5" />
                  )}
                  {searchLoading ? 'Searching...' : 'Search Caregivers'}
                </button>
                <button
                  onClick={() => {
                    setSearchName('')
                    setSearchLocation('')
                    setSelectedSpecialty('')
                    setSortBy('name')
                    setMaxRate(200)
                    // Automatically search after clearing to show all results
                    setTimeout(() => handleSearch(), 100)
                  }}
                  className="px-6 py-4 rounded-xl font-semibold transition-all duration-300 hover:shadow-md hover:scale-105 border-2"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    color: 'var(--text-primary)',
                    borderColor: 'var(--border-medium)',
                    boxShadow: 'var(--shadow-light)',
                    fontSize: '16px',
                    fontWeight: '600',
                    letterSpacing: '0.025em'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-light)'
                  }}
                  title="Clear all filters and show all caregivers"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          </div>

          {/* Results */}
          {loading ? (
            <div className="text-center py-12">
              <div className="inline-flex items-center gap-3 px-8 py-6 rounded-xl shadow-lg" style={{
                backgroundColor: 'var(--bg-primary)',
                border: '1px solid var(--border-light)'
              }}>
                <div className="w-8 h-8 border-3 border-t-transparent rounded-full animate-spin" style={{
                  borderColor: 'var(--primary)'
                }}></div>
                <span className="text-lg font-medium" style={{ color: 'var(--text-primary)' }}>Loading caregivers...</span>
              </div>
            </div>
          ) : filteredCaregivers.length === 0 ? (
            <div className="text-center py-16">
              <div className="w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-6" style={{
                backgroundColor: 'var(--bg-secondary)'
              }}>
                <Users className="w-12 h-12" style={{ color: 'var(--text-secondary)' }} />
              </div>
              <h3 className="text-2xl font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>
                No caregivers found
              </h3>
              <p className="text-lg mb-6 max-w-lg mx-auto" style={{ color: 'var(--text-secondary)', lineHeight: '1.6' }}>
                We couldn't find any caregivers matching your criteria. Try adjusting your search filters, expanding your location range, or browsing all available providers.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center mb-4">
                <button
                  onClick={() => {
                    setSearchName('')
                    setSearchLocation('')
                    setSelectedSpecialty('')
                    handleSearch()
                  }}
                  className="px-6 py-3 rounded-xl font-medium transition-all duration-200 hover:shadow-lg"
                  style={{
                    backgroundColor: 'var(--primary)',
                    color: 'var(--text-white)'
                  }}
                >
                  View All Caregivers
                </button>
                <button
                  onClick={() => {
                    setSearchLocation('')
                    handleSearch()
                  }}
                  className="px-6 py-3 rounded-xl font-medium transition-all duration-200 hover:shadow-md border"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    color: 'var(--text-primary)',
                    borderColor: 'var(--border-light)'
                  }}
                >
                  Remove Location Filter
                </button>
              </div>
              <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                New caregivers join our platform regularly. Check back soon for more options.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredCaregivers.map((caregiver) => (
                <div
                  key={caregiver.id}
                  className="rounded-2xl p-8 transition-all duration-300 hover:-translate-y-1 cursor-pointer"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    border: '2px solid var(--border-light)',
                    boxShadow: 'var(--shadow-card)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)'
                    e.currentTarget.style.borderColor = 'var(--border-medium)'
                    e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                  }}
                  onClick={() => navigate(`/caregiver/${caregiver.id}`)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault()
                      navigate(`/caregiver/${caregiver.id}`)
                    }
                  }}
                  tabIndex={0}
                  role="button"
                  aria-label={`View profile for ${caregiver.full_name || caregiver.name || 'caregiver'}`}
                >
                  <div className="flex items-start gap-4 mb-4">
                    <div className="w-16 h-16 rounded-full flex items-center justify-center flex-shrink-0" style={{
                      backgroundColor: 'var(--primary)',
                      boxShadow: 'var(--shadow-sm)'
                    }}>
                      {!imageErrors.includes(caregiver.id) && caregiver.avatar_url ? (
                        <img
                          src={caregiver.avatar_url}
                          alt={caregiver.full_name || caregiver.name || 'Caregiver'}
                          className="w-full h-full rounded-full object-cover"
                          onError={() => handleImageError(caregiver.id)}
                        />
                      ) : (
                        <span className="text-2xl font-bold" style={{ color: 'var(--text-white)' }}>
                          {(caregiver.full_name || caregiver.name || 'C')[0].toUpperCase()}
                        </span>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-xl font-medium mb-1" style={{ color: 'var(--text-primary)', fontWeight: '500' }}>
                        {caregiver.full_name || caregiver.name}
                      </h3>
                      <div className="flex items-center gap-2 mb-2">
                        {caregiver.availability_status && (
                          <div className="flex items-center gap-1">
                            <div 
                              className="w-2 h-2 rounded-full" 
                              style={{ 
                                backgroundColor: caregiver.availability_status === 'available' ? 'var(--primary)' : 
                                                caregiver.availability_status === 'busy' ? '#f59e0b' : '#6b7280'
                              }}
                            ></div>
                            <span className="text-sm font-medium" style={{ 
                              color: caregiver.availability_status === 'available' ? 'var(--primary)' : 'var(--text-secondary)'
                            }}>
                              {caregiver.availability_status === 'available' ? 'Available' : 
                               caregiver.availability_status === 'busy' ? 'Busy' : 'Offline'}
                            </span>
                          </div>
                        )}
                        {caregiver.verified && (
                          <div className="flex items-center gap-1">
                            <Shield className="w-4 h-4" style={{ color: 'var(--primary)' }} />
                            <span className="text-sm font-medium" style={{ color: 'var(--primary)' }}>
                              Verified
                            </span>
                          </div>
                        )}
                        {(caregiver.average_rating || caregiver.rating) && (
                          <div className="flex items-center gap-1">
                            <Star className="w-4 h-4 fill-current" style={{ color: '#fbbf24' }} />
                            <span className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
                              {(caregiver.average_rating || caregiver.rating).toFixed(1)}
                            </span>
                          </div>
                        )}
                      </div>
                      <div className="flex items-center gap-1 mb-2">
                        <MapPin className="w-4 h-4" style={{ color: 'var(--text-secondary)' }} />
                        <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                          {caregiver.location}
                        </span>
                      </div>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        toggleFavorite(caregiver.id)
                      }}
                      className="p-2 rounded-full transition-colors"
                      style={{
                        backgroundColor: favorites.includes(caregiver.id) ? 'var(--primary)' : 'var(--bg-secondary)'
                      }}
                    >
                      <Heart className={`w-5 h-5 ${favorites.includes(caregiver.id) ? 'fill-current text-white' : ''}`} style={{
                        color: favorites.includes(caregiver.id) ? 'white' : 'var(--text-secondary)'
                      }} />
                    </button>
                  </div>
                  
                  {caregiver.bio && (
                    <p className="text-sm mb-4 line-clamp-2" style={{ color: 'var(--text-secondary)', lineHeight: '1.5' }}>
                      {caregiver.bio}
                    </p>
                  )}

                  <div className="flex items-center justify-between pt-4 border-t" style={{ borderColor: 'var(--border-light)' }}>
                    <div>
                      {caregiver.hourly_rate && (
                        <span className="text-xl font-semibold" style={{ color: 'var(--text-primary)', fontWeight: '600' }}>
                          ${caregiver.hourly_rate}/hr
                        </span>
                      )}
                      {caregiver.years_experience && (
                        <div className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                          {caregiver.years_experience} years exp.
                        </div>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          // Handle message action
                        }}
                        className="p-2 rounded-lg transition-colors"
                        style={{ backgroundColor: 'var(--bg-secondary)' }}
                      >
                        <MessageCircle className="w-5 h-5" style={{ color: 'var(--primary)' }} />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </main>
  )
}
