import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Upload, User, MapPin, Clock, DollarSign, Shield, Star, CheckCircle } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface CaregiverFormData {
  first_name: string
  last_name: string
  email: string
  phone: string
  location: string
  bio: string
  specialties: string[]
  experience_years: number
  hourly_rate: number
  availability_status: string
  certifications: string[]
  languages: string[]
  profile_image?: File
}

export default function ProvideCare() {
  const navigate = useNavigate()
  const [currentStep, setCurrentStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({})

  const [formData, setFormData] = useState<CaregiverFormData>({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    location: '',
    bio: '',
    specialties: [],
    experience_years: 0,
    hourly_rate: 25,
    availability_status: 'available',
    certifications: [],
    languages: ['English'],
    profile_image: undefined
  })

  const specialtyOptions = [
    'Elderly Care', 'Child Care', 'Disability Support', 'Companionship',
    'Medical Care', 'Dementia Care', 'Post-Surgery Care', 'Respite Care',
    'Housekeeping', 'Meal Preparation', 'Transportation', 'Pet Care'
  ]

  const certificationOptions = [
    'CPR Certified', 'First Aid', 'CNA', 'HHA', 'RN', 'LPN',
    'Background Check Completed', 'Drug Test Passed', 'References Available'
  ]

  const languageOptions = [
    'English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese',
    'Chinese', 'Japanese', 'Korean', 'Arabic', 'Russian', 'Hindi'
  ]

  const validateStep = (step: number): boolean => {
    const errors: {[key: string]: string} = {}

    if (step === 1) {
      if (!formData.first_name.trim()) errors.first_name = 'First name is required'
      if (!formData.last_name.trim()) errors.last_name = 'Last name is required'
      if (!formData.email.trim()) {
        errors.email = 'Email is required'
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        errors.email = 'Please enter a valid email address'
      }
      if (!formData.phone.trim()) {
        errors.phone = 'Phone number is required'
      } else if (!/^\(\d{3}\) \d{3}-\d{4}$/.test(formData.phone)) {
        errors.phone = 'Phone must be in format (*************'
      }
      if (!formData.location.trim()) errors.location = 'Location is required'
    }

    if (step === 2) {
      if (!formData.bio.trim()) {
        errors.bio = 'Bio is required'
      } else if (formData.bio.length < 50) {
        errors.bio = 'Bio must be at least 50 characters'
      } else if (formData.bio.length > 500) {
        errors.bio = 'Bio must be less than 500 characters'
      }
      if (formData.specialties.length === 0) {
        errors.specialties = 'Please select at least one specialty'
      }
      if (formData.experience_years < 0 || formData.experience_years > 50) {
        errors.experience_years = 'Experience must be between 0 and 50 years'
      }
    }

    if (step === 3) {
      console.log('Step 3 validation - hourly_rate:', formData.hourly_rate, typeof formData.hourly_rate)
      console.log('Step 3 validation - certifications:', formData.certifications, formData.certifications.length)

      if (formData.hourly_rate < 10 || formData.hourly_rate > 200) {
        errors.hourly_rate = 'Hourly rate must be between $10 and $200'
        console.log('Hourly rate validation failed')
      }
      if (formData.certifications.length === 0) {
        errors.certifications = 'Please select at least one certification'
        console.log('Certifications validation failed')
      }
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleInputChange = (field: keyof CaregiverFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))

    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const handleArrayToggle = (field: 'specialties' | 'certifications' | 'languages', value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].includes(value)
        ? prev[field].filter(item => item !== value)
        : [...prev[field], value]
    }))
  }

  const formatPhoneNumber = (value: string) => {
    const numbers = value.replace(/\D/g, '')
    if (numbers.length <= 3) return numbers
    if (numbers.length <= 6) return `(${numbers.slice(0, 3)}) ${numbers.slice(3)}`
    return `(${numbers.slice(0, 3)}) ${numbers.slice(3, 6)}-${numbers.slice(6, 10)}`
  }

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneNumber(e.target.value)
    handleInputChange('phone', formatted)
  }

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        setValidationErrors(prev => ({
          ...prev,
          profile_image: 'Image must be less than 5MB'
        }))
        return
      }
      if (!file.type.startsWith('image/')) {
        setValidationErrors(prev => ({
          ...prev,
          profile_image: 'Please select an image file'
        }))
        return
      }
      handleInputChange('profile_image', file)
    }
  }

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => prev + 1)
    }
  }

  const prevStep = () => {
    setCurrentStep(prev => prev - 1)
  }

  const handleSubmit = async () => {
    console.log('Form submission started')
    console.log('Form data:', JSON.stringify(formData, null, 2))
    console.log('Step 3 validation:', validateStep(3))

    if (!validateStep(3)) {
      console.log('Step 3 validation failed')
      return
    }

    setLoading(true)
    setError(null)

    try {
      // Upload profile image if provided
      let profile_image_url = null
      if (formData.profile_image) {
        const fileExt = formData.profile_image.name.split('.').pop()
        const fileName = `${Date.now()}.${fileExt}`

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('avatars')
          .upload(fileName, formData.profile_image)

        if (uploadError) throw uploadError

        const { data: { publicUrl } } = supabase.storage
          .from('avatars')
          .getPublicUrl(fileName)

        profile_image_url = publicUrl
      }

      // Create auth user first
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: formData.email,
        password: 'TempPassword123!', // Temporary password - user will need to reset
        options: {
          data: {
            first_name: formData.first_name,
            last_name: formData.last_name,
            full_name: `${formData.first_name} ${formData.last_name}`
          }
        }
      })

      if (authError) throw authError
      if (!authData.user) throw new Error('Failed to create user account')

      // Now create the profile with the auth user's ID
      console.log('Creating profile with auth user ID:', authData.user.id)
      console.log('Profile data to insert:', {
        id: authData.user.id,
        first_name: formData.first_name,
        last_name: formData.last_name,
        email: formData.email,
        languages: formData.languages,
        preferences: {
          bio: formData.bio,
          specialties: formData.specialties,
          hourly_rate: formData.hourly_rate,
          availability_status: formData.availability_status,
          certifications: formData.certifications
        }
      })

      const { data, error: dbError } = await supabase
        .schema('care_connector')
        .from('profiles')
        .insert({
          id: authData.user.id,
          first_name: formData.first_name,
          last_name: formData.last_name,
          full_name: `${formData.first_name} ${formData.last_name}`,
          email: formData.email,
          phone_number: formData.phone,
          location: formData.location,
          languages: formData.languages,
          avatar_url: profile_image_url,
          role: 'caregiver',
          is_verified: false,
          average_rating: 0,
          years_of_experience: formData.experience_years,
          reviews_count: 0,
          // Store additional caregiver data in preferences field as JSON
          preferences: JSON.stringify({
            bio: formData.bio,
            specialties: formData.specialties,
            hourly_rate: formData.hourly_rate,
            availability_status: formData.availability_status,
            certifications: formData.certifications
          })
        })
        .select()

      if (dbError) {
        console.error('Database error details:', {
          message: dbError.message,
          details: dbError.details,
          hint: dbError.hint,
          code: dbError.code
        })
        throw dbError
      }

      console.log('Registration successful:', data)
      if (data && data.length > 0) {
        setSuccess(true)
        setTimeout(() => {
          navigate('/caregivers')
        }, 3000)
      } else {
        throw new Error('Profile creation failed - no data returned')
      }

    } catch (err) {
      console.error('Registration error:', err)
      // More detailed error handling
      let errorMessage = 'Registration failed. Please try again.'
      if (err instanceof Error) {
        errorMessage = err.message
        console.error('Error details:', {
          message: err.message,
          stack: err.stack,
          name: err.name
        })
      }
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="max-w-md mx-auto text-center p-8">
          <div className="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6" style={{backgroundColor: 'var(--bg-success-light)'}}>
            <CheckCircle className="w-10 h-10" style={{color: 'var(--text-success)'}} />
          </div>
          <h2 className="text-2xl font-semibold mb-4" style={{color: 'var(--text-primary)'}}>
            Registration Successful!
          </h2>
          <p className="text-base mb-6" style={{color: 'var(--text-secondary)'}}>
            Your caregiver profile has been created. You'll be redirected to browse other caregivers shortly.
          </p>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-green-500 h-2 rounded-full animate-pulse" style={{width: '100%'}}></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
      {/* Header */}
      <div className="px-8 py-16" style={{ backgroundColor: 'var(--bg-primary)', borderBottom: '1px solid var(--border-light)' }}>
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-6xl font-light tracking-tight leading-tight mb-10" style={{
            color: 'var(--text-primary)',
            fontWeight: '200',
            letterSpacing: '-0.02em',
            textShadow: 'var(--shadow-text)'
          }}>
            Become a Care Provider
          </h1>
          <p className="text-xl font-normal leading-relaxed max-w-3xl mx-auto mb-10" style={{
            color: 'var(--text-secondary)',
            fontWeight: '400',
            lineHeight: '1.7'
          }}>
            Join our community of trusted caregivers and help families in your area receive the personalized care they deserve
          </p>
          
          {/* Progress Indicator - Enhanced Apple Style */}
          <div className="flex items-center justify-center gap-20 mb-16 relative">
            {[
              { num: 1, title: 'Personal Information' },
              { num: 2, title: 'Experience & Skills' },
              { num: 3, title: 'Verification & Review' }
            ].map((step, index) => (
              <div key={step.num} className="flex flex-col items-center z-10">
                <div
                  className="w-14 h-14 rounded-full flex items-center justify-center font-semibold transition-all duration-300 shadow-lg mb-3"
                  style={{
                    backgroundColor: currentStep >= step.num ? 'var(--primary)' : 'var(--bg-primary)',
                    color: currentStep >= step.num ? 'var(--bg-primary)' : 'var(--text-secondary)',
                    border: currentStep >= step.num ? 'none' : '2px solid var(--border-light)',
                    boxShadow: currentStep >= step.num ? 'var(--shadow-medium)' : 'var(--shadow-light)',
                    transform: currentStep === step.num ? 'scale(1.1)' : 'scale(1)'
                  }}
                >
                  {currentStep > step.num ? (
                    <CheckCircle className="w-6 h-6" style={{ color: 'var(--bg-primary)' }} />
                  ) : (
                    step.num
                  )}
                </div>
                <span className="text-sm font-medium" style={{
                  color: currentStep >= step.num ? 'var(--primary)' : 'var(--text-secondary)'
                }}>
                  {step.title}
                </span>
                {step.num < 3 && (
                  <div
                    className="absolute w-24 h-1 rounded-full transition-all duration-300"
                    style={{
                      backgroundColor: currentStep > step.num ? 'var(--primary)' : 'var(--border-light)',
                      left: `calc(50% + ${index * 140 + 40}px)`,
                      top: '28px',
                      zIndex: 1,
                      boxShadow: 'var(--shadow-light)'
                    }}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Form */}
      <main className="px-8 py-16" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="max-w-3xl mx-auto">
          {error && (
            <div className="mb-12 p-6 rounded-2xl" style={{
              backgroundColor: 'var(--bg-error-light)', 
              border: '1px solid var(--border-error)',
              boxShadow: 'var(--shadow-card)'
            }}>
              <p className="font-medium" style={{color: 'var(--text-error)'}}>{error}</p>
            </div>
          )}

          <div className="rounded-3xl p-12" style={{
            backgroundColor: 'var(--bg-primary)',
            border: 'none',
            boxShadow: 'var(--shadow-card-hover)',
            backdropFilter: 'blur(10px)'
          }}>
            {/* Step 1: Personal Information */}
            {currentStep === 1 && (
              <div>
                <h2 className="text-3xl font-light tracking-tight leading-tight mb-8" style={{
                  color: 'var(--text-primary)',
                  fontWeight: '300',
                  letterSpacing: '-0.02em'
                }}>
                  Personal Information
                </h2>

                <div className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="first_name" className="block text-sm font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                        First Name *
                      </label>
                      <input
                        id="first_name"
                        type="text"
                        value={formData.first_name}
                        onChange={(e) => handleInputChange('first_name', e.target.value)}
                        className="w-full px-5 py-4 rounded-xl border-2 transition-all duration-300 focus:outline-none focus:ring-0 hover:scale-[1.02]"
                        style={{
                          borderColor: validationErrors.first_name ? 'var(--error)' : 'var(--border-light)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)',
                          boxShadow: 'var(--shadow-input)',
                          fontSize: '16px',
                          fontWeight: '400',
                          backdropFilter: 'blur(10px)'
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = 'var(--primary)'
                          e.target.style.boxShadow = 'var(--shadow-input-focus)'
                          e.target.style.backgroundColor = 'var(--bg-secondary)'
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = validationErrors.first_name ? 'var(--error)' : 'var(--border-light)'
                          e.target.style.boxShadow = 'var(--shadow-input)'
                          e.target.style.backgroundColor = 'var(--bg-primary)'
                        }}
                        aria-invalid={!!validationErrors.first_name}
                        aria-describedby={validationErrors.first_name ? "first_name-error" : undefined}
                      />
                      {validationErrors.first_name && (
                        <p id="first_name-error" className="text-sm mt-1" style={{color: 'var(--error)'}}>
                          {validationErrors.first_name}
                        </p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="last_name" className="block text-sm font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                        Last Name *
                      </label>
                      <input
                        id="last_name"
                        type="text"
                        value={formData.last_name}
                        onChange={(e) => handleInputChange('last_name', e.target.value)}
                        className="w-full px-5 py-4 rounded-xl border-2 transition-all duration-300 focus:outline-none focus:ring-0 hover:scale-[1.02]"
                        style={{
                          borderColor: validationErrors.last_name ? 'var(--error)' : 'var(--border-light)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)',
                          boxShadow: 'var(--shadow-input)',
                          fontSize: '16px',
                          fontWeight: '400',
                          backdropFilter: 'blur(10px)'
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = 'var(--primary)'
                          e.target.style.boxShadow = 'var(--shadow-input-focus)'
                          e.target.style.backgroundColor = 'var(--bg-secondary)'
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = validationErrors.last_name ? 'var(--error)' : 'var(--border-light)'
                          e.target.style.boxShadow = 'var(--shadow-input)'
                          e.target.style.backgroundColor = 'var(--bg-primary)'
                        }}
                        aria-invalid={!!validationErrors.last_name}
                        aria-describedby={validationErrors.last_name ? "last_name-error" : undefined}
                      />
                      {validationErrors.last_name && (
                        <p id="last_name-error" className="text-sm mt-1" style={{color: 'var(--error)'}}>
                          {validationErrors.last_name}
                        </p>
                      )}
                    </div>
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                      Email Address *
                    </label>
                    <input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="w-full px-4 py-3 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-50 hover:border-gray-400"
                      style={{
                        borderColor: validationErrors.email ? 'var(--error)' : 'var(--border-light)',
                        backgroundColor: 'var(--bg-primary)',
                        color: 'var(--text-primary)',
                        boxShadow: 'var(--shadow-input)',
                        fontSize: '16px',
                        fontWeight: '400'
                      }}
                      onFocus={(e) => {
                        e.target.style.borderColor = 'var(--primary)'
                        e.target.style.boxShadow = 'var(--shadow-input-focus)'
                      }}
                      onBlur={(e) => {
                        e.target.style.borderColor = validationErrors.email ? 'var(--error)' : 'var(--border-light)'
                        e.target.style.boxShadow = 'var(--shadow-input)'
                      }}
                      aria-invalid={!!validationErrors.email}
                      aria-describedby={validationErrors.email ? "email-error" : undefined}
                    />
                    {validationErrors.email && (
                      <p id="email-error" className="text-sm mt-1" style={{color: 'var(--error)'}}>
                        {validationErrors.email}
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                      Phone Number *
                    </label>
                    <input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handlePhoneChange}
                      placeholder="(*************"
                      className="w-full px-4 py-3 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-50 hover:border-gray-400"
                      style={{
                        borderColor: validationErrors.phone ? 'var(--error)' : 'var(--border-light)',
                        backgroundColor: 'var(--bg-primary)',
                        color: 'var(--text-primary)',
                        boxShadow: 'var(--shadow-input)',
                        fontSize: '16px',
                        fontWeight: '400'
                      }}
                      onFocus={(e) => {
                        e.target.style.borderColor = 'var(--primary)'
                        e.target.style.boxShadow = 'var(--shadow-input-focus)'
                      }}
                      onBlur={(e) => {
                        e.target.style.borderColor = validationErrors.phone ? 'var(--error)' : 'var(--border-light)'
                        e.target.style.boxShadow = 'var(--shadow-input)'
                      }}
                      aria-invalid={!!validationErrors.phone}
                      aria-describedby={validationErrors.phone ? "phone-error" : undefined}
                    />
                    {validationErrors.phone && (
                      <p id="phone-error" className="text-sm mt-1" style={{color: 'var(--error)'}}>
                        {validationErrors.phone}
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="location" className="block text-sm font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                      Location *
                    </label>
                    <div className="relative">
                      <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5" style={{color: 'var(--text-muted)'}} />
                      <input
                        id="location"
                        type="text"
                        value={formData.location}
                        onChange={(e) => handleInputChange('location', e.target.value)}
                        placeholder="City, State"
                        className="w-full pl-12 pr-4 py-3 rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-50"
                        style={{
                          borderColor: validationErrors.location ? 'var(--error)' : 'var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)'
                        }}
                        aria-invalid={!!validationErrors.location}
                        aria-describedby={validationErrors.location ? "location-error" : undefined}
                      />
                    </div>
                    {validationErrors.location && (
                      <p id="location-error" className="text-sm mt-1" style={{color: 'var(--error)'}}>
                        {validationErrors.location}
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex justify-end mt-10">
                  <button
                    onClick={nextStep}
                    className="px-12 py-5 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:scale-[1.05] hover:-translate-y-1 focus:outline-none focus:ring-4 focus:ring-opacity-30 active:scale-95"
                    style={{
                      backgroundColor: 'var(--primary)',
                      color: 'var(--bg-primary)',
                      boxShadow: 'var(--shadow-medium)',
                      fontSize: '17px',
                      fontWeight: '600',
                      letterSpacing: '0.015em',
                      backdropFilter: 'blur(10px)',
                      border: '1px solid rgba(16, 185, 129, 0.3)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--primary-hover)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)'
                      e.currentTarget.style.transform = 'scale(1.05) translateY(-2px)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--primary)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
                      e.currentTarget.style.transform = 'scale(1) translateY(0)'
                    }}
                  >
                    Continue to Experience →
                  </button>
                </div>
              </div>
            )}

            {/* Step 2: Professional Information */}
            {currentStep === 2 && (
              <div>
                <h2 className="text-3xl font-light tracking-tight leading-tight mb-8" style={{
                  color: 'var(--text-primary)',
                  fontWeight: '300',
                  letterSpacing: '-0.02em'
                }}>
                  Professional Information
                </h2>

                <div className="space-y-6">
                  <div>
                    <label htmlFor="bio" className="block text-sm font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                      Bio / About You *
                    </label>
                    <textarea
                      id="bio"
                      value={formData.bio}
                      onChange={(e) => handleInputChange('bio', e.target.value)}
                      placeholder="Tell families about your experience, approach to care, and what makes you special..."
                      rows={4}
                      className="w-full px-4 py-3 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-50 resize-none hover:border-gray-400"
                      style={{
                        borderColor: validationErrors.bio ? 'var(--error)' : 'var(--border-light)',
                        backgroundColor: 'var(--bg-primary)',
                        color: 'var(--text-primary)',
                        boxShadow: 'var(--shadow-input)',
                        fontSize: '16px',
                        fontWeight: '400',
                        lineHeight: '1.5'
                      }}
                      onFocus={(e) => {
                        e.target.style.borderColor = 'var(--primary)'
                        e.target.style.boxShadow = 'var(--shadow-input-focus)'
                      }}
                      onBlur={(e) => {
                        e.target.style.borderColor = validationErrors.bio ? 'var(--error)' : 'var(--border-light)'
                        e.target.style.boxShadow = 'var(--shadow-input)'
                      }}
                      aria-invalid={!!validationErrors.bio}
                      aria-describedby={validationErrors.bio ? "bio-error" : undefined}
                    />
                    <div className="flex justify-between items-center mt-1">
                      {validationErrors.bio && (
                        <p id="bio-error" className="text-sm" style={{color: 'var(--error)'}}>
                          {validationErrors.bio}
                        </p>
                      )}
                      <p className="text-sm ml-auto" style={{color: 'var(--text-muted)'}}>
                        {formData.bio.length}/500 characters
                      </p>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-3" style={{color: 'var(--text-primary)'}}>
                      Specialties * (Select all that apply)
                    </label>
                    <div className="grid grid-cols-2 gap-3">
                      {specialtyOptions.map((specialty) => (
                        <label key={specialty} className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-all duration-200 hover:bg-gray-50">
                          <input
                            type="checkbox"
                            checked={formData.specialties.includes(specialty)}
                            onChange={() => handleArrayToggle('specialties', specialty)}
                            className="rounded"
                            style={{accentColor: 'var(--primary)'}}
                          />
                          <span className="text-sm" style={{color: 'var(--text-primary)'}}>{specialty}</span>
                        </label>
                      ))}
                    </div>
                    {validationErrors.specialties && (
                      <p className="text-sm mt-2" style={{color: 'var(--error)'}}>
                        {validationErrors.specialties}
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="experience_years" className="block text-sm font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                      Years of Experience *
                    </label>
                    <div className="relative">
                      <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5" style={{color: 'var(--text-muted)'}} />
                      <input
                        id="experience_years"
                        type="number"
                        min="0"
                        max="50"
                        value={formData.experience_years}
                        onChange={(e) => handleInputChange('experience_years', parseInt(e.target.value) || 0)}
                        className="w-full pl-12 pr-4 py-3 rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-50"
                        style={{
                          borderColor: validationErrors.experience_years ? 'var(--error)' : 'var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)'
                        }}
                        aria-invalid={!!validationErrors.experience_years}
                        aria-describedby={validationErrors.experience_years ? "experience_years-error" : undefined}
                      />
                    </div>
                    {validationErrors.experience_years && (
                      <p id="experience_years-error" className="text-sm mt-1" style={{color: 'var(--error)'}}>
                        {validationErrors.experience_years}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-3" style={{color: 'var(--text-primary)'}}>
                      Languages Spoken (Select all that apply)
                    </label>
                    <div className="grid grid-cols-3 gap-3">
                      {languageOptions.map((language) => (
                        <label key={language} className="flex items-center space-x-2 p-2 border rounded-lg cursor-pointer transition-all duration-200 hover:bg-gray-50">
                          <input
                            type="checkbox"
                            checked={formData.languages.includes(language)}
                            onChange={() => handleArrayToggle('languages', language)}
                            className="rounded"
                            style={{accentColor: 'var(--primary)'}}
                          />
                          <span className="text-sm" style={{color: 'var(--text-primary)'}}>{language}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="flex justify-between mt-8">
                  <button
                    onClick={prevStep}
                    className="px-10 py-4 rounded-xl font-semibold transition-all duration-200 shadow-md hover:scale-105 focus:outline-none focus:ring-4 focus:ring-opacity-30"
                    style={{
                      backgroundColor: 'var(--bg-primary)',
                      color: 'var(--text-primary)',
                      border: '2px solid var(--border-light)',
                      boxShadow: 'var(--shadow-light)',
                      fontSize: '16px',
                      fontWeight: '600',
                      letterSpacing: '0.025em'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-light)'
                    }}
                  >
                    Previous
                  </button>
                  <button
                    onClick={nextStep}
                    className="px-10 py-4 rounded-xl font-semibold transition-all duration-200 shadow-lg hover:scale-105 focus:outline-none focus:ring-4 focus:ring-opacity-30"
                    style={{
                      backgroundColor: 'var(--primary)',
                      color: 'var(--bg-primary)',
                      boxShadow: 'var(--shadow-medium)',
                      fontSize: '16px',
                      fontWeight: '600',
                      letterSpacing: '0.025em'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--primary-hover)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--primary)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
                    }}
                  >
                    Next Step
                  </button>
                </div>
              </div>
            )}

            {/* Step 3: Rates & Certifications */}
            {currentStep === 3 && (
              <div>
                <h2 className="text-2xl font-semibold mb-6" style={{color: 'var(--text-primary)'}}>
                  Rates & Certifications
                </h2>

                <div className="space-y-6">
                  <div>
                    <label htmlFor="hourly_rate" className="block text-sm font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                      Hourly Rate * (USD)
                    </label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5" style={{color: 'var(--text-muted)'}} />
                      <input
                        id="hourly_rate"
                        type="number"
                        min="10"
                        max="200"
                        value={formData.hourly_rate}
                        onChange={(e) => {
                          const value = e.target.value === '' ? 25 : parseInt(e.target.value) || 25
                          handleInputChange('hourly_rate', value)
                        }}
                        className="w-full pl-12 pr-4 py-3 rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-50"
                        style={{
                          borderColor: validationErrors.hourly_rate ? 'var(--error)' : 'var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)'
                        }}
                        aria-invalid={!!validationErrors.hourly_rate}
                        aria-describedby={validationErrors.hourly_rate ? "hourly_rate-error" : undefined}
                      />
                    </div>
                    {validationErrors.hourly_rate && (
                      <p id="hourly_rate-error" className="text-sm mt-1" style={{color: 'var(--error)'}}>
                        {validationErrors.hourly_rate}
                      </p>
                    )}
                    <p className="text-sm mt-1" style={{color: 'var(--text-muted)'}}>
                      Competitive rates in your area: $15-$35/hour
                    </p>
                  </div>

                  <div>
                    <label htmlFor="availability_status" className="block text-sm font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                      Availability Status
                    </label>
                    <select
                      id="availability_status"
                      value={formData.availability_status}
                      onChange={(e) => handleInputChange('availability_status', e.target.value)}
                      className="w-full px-4 py-3 rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-50"
                      style={{
                        borderColor: 'var(--border-medium)',
                        backgroundColor: 'var(--bg-primary)',
                        color: 'var(--text-primary)'
                      }}
                    >
                      <option value="available">Available Now</option>
                      <option value="limited">Limited Availability</option>
                      <option value="unavailable">Currently Unavailable</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-3" style={{color: 'var(--text-primary)'}}>
                      Certifications & Qualifications * (Select all that apply)
                    </label>
                    <div className="grid grid-cols-2 gap-3">
                      {certificationOptions.map((cert) => (
                        <label key={cert} className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-all duration-200 hover:bg-gray-50">
                          <input
                            type="checkbox"
                            checked={formData.certifications.includes(cert)}
                            onChange={() => handleArrayToggle('certifications', cert)}
                            className="rounded"
                            style={{accentColor: 'var(--primary)'}}
                          />
                          <span className="text-sm" style={{color: 'var(--text-primary)'}}>{cert}</span>
                        </label>
                      ))}
                    </div>
                    {validationErrors.certifications && (
                      <p className="text-sm mt-2" style={{color: 'var(--error)'}}>
                        {validationErrors.certifications}
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="profile_image" className="block text-sm font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                      Profile Photo (Optional)
                    </label>
                    <div className="border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200 hover:border-gray-400" style={{borderColor: 'var(--border-light)'}}>
                      <Upload className="mx-auto w-8 h-8 mb-3" style={{color: 'var(--text-muted)'}} />
                      <input
                        id="profile_image"
                        type="file"
                        accept="image/*"
                        onChange={handleFileUpload}
                        className="hidden"
                      />
                      <label htmlFor="profile_image" className="cursor-pointer">
                        <span className="text-sm font-medium" style={{color: 'var(--primary)'}}>
                          Click to upload
                        </span>
                        <span className="text-sm ml-1" style={{color: 'var(--text-muted)'}}>
                          or drag and drop
                        </span>
                      </label>
                      <p className="text-xs mt-1" style={{color: 'var(--text-muted)'}}>
                        PNG, JPG up to 5MB
                      </p>
                      {formData.profile_image && (
                        <p className="text-sm mt-2 font-medium" style={{color: 'var(--text-success)'}}>
                          ✓ {formData.profile_image.name}
                        </p>
                      )}
                    </div>
                    {validationErrors.profile_image && (
                      <p className="text-sm mt-1" style={{color: 'var(--error)'}}>
                        {validationErrors.profile_image}
                      </p>
                    )}
                  </div>
                </div>

                <div className="flex justify-between mt-8">
                  <button
                    onClick={prevStep}
                    className="px-8 py-3 rounded-lg font-medium transition-all duration-200"
                    style={{
                      backgroundColor: 'var(--bg-secondary)',
                      color: 'var(--text-primary)',
                      border: '1px solid var(--border-medium)'
                    }}
                  >
                    Previous
                  </button>
                  <button
                    onClick={handleSubmit}
                    disabled={loading}
                    className="px-8 py-3 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    style={{
                      backgroundColor: loading ? 'var(--text-muted)' : 'var(--primary)',
                      color: 'white'
                    }}
                  >
                    {loading ? 'Creating Profile...' : 'Complete Registration'}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  )
}
