import { useState, useEffect, useRef } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { Search, Users, Sparkles, Calendar, MessageSquare, CheckSquare, Shield, UserCheck, ChevronDown, CheckCircle, Award } from 'lucide-react'
import { dataService } from '../lib/dataService'

export default function Home() {
  const [showFindCareDropdown, setShowFindCareDropdown] = useState(false)
  const [caregivers, setCaregivers] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [homepageStats, setHomepageStats] = useState({
    verifiedProfessionals: 0,
    averageRating: null,
    successfulBookings: 0,
    supportStatus: null
  })
  const [heroContent, setHeroContent] = useState({
    title: 'Your Complete Care Network',
    subtitle: 'Connect with verified healthcare professionals and build your personalized care team'
  })
  const [securityBadges, setSecurityBadges] = useState([
    { id: 1, text: 'HIPAA Compliant', icon: 'CheckCircle' },
    { id: 2, text: 'Background Verified', icon: 'Shield' },
    { id: 3, text: 'Licensed & Insured', icon: 'Award' }
  ])
  const [searchContent, setSearchContent] = useState({
    title: 'Find Your Perfect Care Match',
    subtitle: 'Search by location, specialty, and availability',
    locationHelp: 'Enter your city, state, or ZIP code to find nearby care providers'
  })
  const [featuresContent, setFeaturesContent] = useState([
    {
      id: 1,
      title: 'Build Your Care Group',
      description: 'Coordinate care seamlessly between family members, caregivers, and healthcare professionals.',
      icon: 'Users',
      buttonText: 'Create Care Group',
      action: 'create-group'
    },
    {
      id: 2,
      title: 'AI-Powered Assistance',
      description: 'Smart healthcare coordination that anticipates needs and optimizes care outcomes.',
      icon: 'Sparkles',
      buttonText: 'Try AI Assistant',
      action: 'ai-helper'
    }
  ])
  
  // Essential Care Tools section state
  const [essentialToolsContent, setEssentialToolsContent] = useState({
    title: 'Essential Care Tools',
    description: 'Stay connected and organized with integrated care management tools.',
    tools: [
      {
        id: 1,
        name: 'Shared Calendars',
        route: '/shared-calendars',
        icon: 'Calendar'
      },
      {
        id: 2,
        name: 'Secure Messaging',
        route: '/secure-messaging',
        icon: 'MessageSquare'
      },
      {
        id: 3,
        name: 'Task Management',
        route: '/task-management',
        icon: 'CheckSquare'
      }
    ]
  })
  
  // Featured Care Providers section state
  const [featuredProvidersContent, setFeaturedProvidersContent] = useState({
    title: 'Featured Care Providers',
    description: 'Meet our top-rated, verified healthcare professionals ready to provide exceptional care.'
  })
  
  // Provider Network section state
  const [providerNetworkContent, setProviderNetworkContent] = useState({
    title: 'Building Our Provider Network',
    description: 'We\'re carefully vetting and onboarding qualified care providers to ensure the highest quality of service.',
    buttonText: 'Join Our Provider Network'
  })
  const [takeControlContent, setTakeControlContent] = useState({
    title: 'Take Control of Your Care Journey',
    description: 'Access powerful tools to manage appointments, track health records, and coordinate with your care team all in one secure platform.'
  })
  const [footerContent, setFooterContent] = useState({
    description: 'Connecting families with trusted care providers through innovative technology and compassionate service.',
    servicesTitle: 'Services',
    learnMoreTitle: 'Learn More'
  })
  const [searchFilters, setSearchFilters] = useState({
    careType: 'all',
    location: '',
    availability: 'any',
    insurance: 'any',
    language: 'any',
    certification: 'any'
  })
  const [searchFilterOptions, setSearchFilterOptions] = useState({
    careTypes: [],
    availability: [],
    insurance: [],
    languages: [],
    certifications: []
  })
  const [navigationOptions, setNavigationOptions] = useState<any[]>([])
  const dropdownRef = useRef<HTMLDivElement>(null)
  const navigate = useNavigate()

  useEffect(() => {
    const fetchData = async () => {
      try {
        setError(null) // Clear any previous errors
        console.log('Fetching homepage data...')

        // Fetch caregivers, homepage statistics, dynamic search filter options, and navigation options from database
        const [caregiversData, statsData, searchOptionsData, navigationOptionsData] = await Promise.all([
          dataService.getCaregivers(),
          dataService.getHomepageStats(),
          dataService.getSearchFilterOptions(),
          dataService.getNavigationOptions()
        ])

        console.log('Caregivers data received:', caregiversData)
        console.log('Homepage stats received:', statsData)

        setCaregivers(caregiversData.slice(0, 3)) // Show only first 3 for homepage
        setHomepageStats(statsData)
        setSearchFilterOptions(searchOptionsData)
        setNavigationOptions(navigationOptionsData)
        // Use static content for now since these methods don't exist in dataService
        setHeroContent({
          title: 'Your Complete Care Network',
          subtitle: 'Connect with verified healthcare professionals and build your personalized care team'
        })
        setSecurityBadges([
          { id: 1, text: 'HIPAA Compliant', icon: 'CheckCircle' },
          { id: 2, text: 'Background Verified', icon: 'Shield' },
          { id: 3, text: 'Licensed & Insured', icon: 'Award' }
        ])
        setSearchContent({
          title: 'Find Your Perfect Care Match',
          subtitle: 'Search by location, specialty, and availability',
          locationHelp: 'Enter your city, state, or ZIP code to find nearby care providers'
        })
        setFeaturesContent([
          {
            id: 1,
            title: 'Build Your Care Group',
            description: 'Coordinate care seamlessly between family members, caregivers, and healthcare professionals.',
            icon: 'Users',
            buttonText: 'Create Care Group',
            action: 'create-group'
          },
          {
            id: 2,
            title: 'AI-Powered Assistance',
            description: 'Smart healthcare coordination that anticipates needs and optimizes care outcomes.',
            icon: 'Sparkles',
            buttonText: 'Try AI Assistant',
            action: 'ai-helper'
          }
        ])
        setEssentialToolsContent({
          title: 'Essential Care Tools',
          description: 'Stay connected and organized with integrated care management tools.',
          tools: [
            {
              id: 1,
              name: 'Shared Calendars',
              route: '/shared-calendars',
              icon: 'Calendar'
            },
            {
              id: 2,
              name: 'Secure Messaging',
              route: '/secure-messaging',
              icon: 'MessageSquare'
            },
            {
              id: 3,
              name: 'Task Management',
              route: '/task-management',
              icon: 'CheckSquare'
            }
          ]
        })
        setFeaturedProvidersContent({
          title: 'Featured Care Providers',
          description: 'Meet our top-rated, verified healthcare professionals ready to provide exceptional care.'
        })
        setProviderNetworkContent({
          title: 'Building Our Provider Network',
          description: 'We\'re carefully vetting and onboarding qualified care providers to ensure the highest quality of service.',
          buttonText: 'Join Our Provider Network'
        })
        setTakeControlContent({
          title: 'Take Control of Your Care Journey',
          description: 'Access powerful tools to manage appointments, track health records, and coordinate with your care team all in one secure platform.'
        })
        setFooterContent({
          description: 'Connecting families with trusted care providers through innovative technology and compassionate service.',
          servicesTitle: 'Services',
          learnMoreTitle: 'Learn More'
        })
      } catch (error) {
        console.error('Error fetching homepage data:', error)
        setError('Failed to load care providers. Please try again later.')
        setCaregivers([])
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  // Handle outside click to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowFindCareDropdown(false)
      }
    }

    if (showFindCareDropdown) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showFindCareDropdown])

  const handleGetStarted = () => {
    navigate('/get-started')
  }

  const handleBrowseProviders = () => {
    navigate('/caregivers')
  }

  const handleSearchSubmit = () => {
    // Validate required location field
    if (!searchFilters.location.trim()) {
      setError('Please enter a location to search for care providers.')
      // Focus on location input for better UX
      const locationInput = document.getElementById('location-input')
      if (locationInput) {
        locationInput.focus()
        locationInput.style.borderColor = 'var(--error)'
      }
      return
    }

    // Clear any previous errors
    setError(null)

    // Build search parameters from filters
    const searchParams = new URLSearchParams()

    if (searchFilters.careType !== 'all') {
      searchParams.set('type', searchFilters.careType)
    }

    if (searchFilters.location.trim()) {
      searchParams.set('location', searchFilters.location.trim())
    }

    if (searchFilters.availability !== 'any') {
      searchParams.set('availability', searchFilters.availability)
    }

    if (searchFilters.insurance !== 'any') {
      searchParams.set('insurance', searchFilters.insurance)
    }

    if (searchFilters.language !== 'any') {
      searchParams.set('language', searchFilters.language)
    }

    if (searchFilters.certification !== 'any') {
      searchParams.set('certification', searchFilters.certification)
    }

    // Navigate to caregivers page with search parameters
    const searchQuery = searchParams.toString()
    navigate(`/caregivers${searchQuery ? `?${searchQuery}` : ''}`)
  }

  const handleFilterChange = (filterType: string, value: string) => {
    setSearchFilters(prev => ({
      ...prev,
      [filterType]: value
    }))
  }

  const handleAIHelper = () => {
    // Navigate to AI assistance page or open AI chat
    navigate('/ai-assistant')
  }

  return (
    <main style={{ minHeight: '100vh', backgroundColor: 'var(--bg-primary)' }}>
      {/* Skip Link for Accessibility */}
      <a href="#main-content" className="skip-link">
        Skip to main content
      </a>

      {/* Hero Section - Clean Apple Mac Desktop Style */}
      <section id="main-content" className="py-20 md:py-24 lg:py-32 px-4 sm:px-6 md:px-8 text-center" style={{
        backgroundColor: 'var(--bg-primary)'
      }} role="banner" aria-label="Hero section">


        <div className="max-w-4xl mx-auto">
          {/* Main Title - Clean Apple Mac Desktop Typography */}
          <div>
            <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-light mb-8 md:mb-10 tracking-tight macos-title" style={{
              color: 'var(--text-primary)',
              lineHeight: '1.05',
              fontWeight: '200',
              letterSpacing: '-0.02em'
            }}>
              {heroContent.title}
            </h1>
            <p className="text-lg sm:text-xl md:text-2xl max-w-3xl mx-auto leading-relaxed mb-12 md:mb-14 macos-subtitle" style={{
              color: 'var(--text-secondary)',
              lineHeight: '1.4',
              fontWeight: '300',
              letterSpacing: '-0.005em'
            }}>
              {heroContent.subtitle}
            </p>
          </div>


        </div>

        {/* Clean CTA Section - Apple Mac Desktop Style */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          {/* Find Care Dropdown - Clean Primary CTA */}
          <div className="relative">
            <button
              onClick={() => setShowFindCareDropdown(!showFindCareDropdown)}
              className="px-10 py-4 rounded-xl text-lg font-medium transition-all duration-300 flex items-center gap-3 shadow-sm hover:shadow-md"
              style={{
                backgroundColor: 'var(--primary)',
                color: 'var(--bg-primary)',
                fontWeight: '600',
                letterSpacing: '-0.005em'
              } as React.CSSProperties}
              onMouseEnter={(e) => {
                e.currentTarget.style.opacity = '0.95'
                e.currentTarget.style.transform = 'translateY(-2px)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.opacity = '1'
                e.currentTarget.style.transform = 'translateY(0)'
              }}
              aria-label="Find care providers"
              aria-expanded={showFindCareDropdown}
              aria-haspopup="true"
              aria-controls="find-care-dropdown"
            >
              <Search className="w-4 h-4" />
              Find Care
              <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${showFindCareDropdown ? 'rotate-180' : ''}`} />
            </button>

            {showFindCareDropdown && (
              <div
                ref={dropdownRef}
                id="find-care-dropdown"
                role="menu"
                aria-orientation="vertical"
                className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 w-72 sm:w-80 rounded-lg shadow-lg z-50"
                style={{backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)', boxShadow: 'var(--shadow-dropdown)'}}
              >
                <div className="py-2">
                  {navigationOptions.map((option) => {
                    // Dynamic icon mapping based on icon string from database
                    const iconMap = {
                      'Users': Users,
                      'Shield': Shield,
                      'CheckSquare': CheckSquare,
                      'UserCheck': UserCheck,
                      'Search': Search,
                      'Calendar': Calendar,
                      'MessageSquare': MessageSquare,
                      'Sparkles': Sparkles
                    }
                    const IconComponent = iconMap[option.icon] || Users

                    return (
                      <Link
                        key={option.id}
                        to={option.route}
                        role="menuitem"
                        tabIndex={showFindCareDropdown ? 0 : -1}
                        className="block px-4 py-3 transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-inset"
                        style={{
                          backgroundColor: 'var(--bg-primary)'
                        } as React.CSSProperties}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                        }}
                        onFocus={(e) => {
                          e.currentTarget.style.boxShadow = '0 0 0 2px var(--primary)'
                        }}
                        onBlur={(e) => {
                          e.currentTarget.style.boxShadow = 'none'
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Escape') {
                            setShowFindCareDropdown(false)
                          }
                        }}
                        onClick={() => setShowFindCareDropdown(false)}
                      >
                        <div className="flex items-center gap-3">
                          <IconComponent className="w-4 h-4" style={{color: 'var(--primary)'}} />
                          <div className="flex-1">
                            <div className="font-medium text-sm" style={{color: 'var(--text-primary)'}}>{option.title}</div>
                            <div className="text-xs" style={{color: 'var(--text-secondary)'}}>{option.description}</div>
                          </div>
                        </div>
                      </Link>
                    )
                  })}
                </div>
              </div>
            )}
          </div>

          <Link
            to="/how-it-works"
            className="px-10 py-4 rounded-xl text-lg font-medium transition-all duration-300 border no-underline flex items-center gap-3 focus:outline-none focus:ring-2 focus:ring-opacity-50 shadow-sm hover:shadow-md"
            style={{
              color: 'var(--text-primary)',
              borderColor: 'var(--border-medium)',
              backgroundColor: 'var(--bg-primary)',
              fontWeight: '500',
              letterSpacing: '-0.005em'
            } as React.CSSProperties}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
              e.currentTarget.style.borderColor = 'var(--primary)'
              e.currentTarget.style.transform = 'translateY(-2px)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
              e.currentTarget.style.borderColor = 'var(--border-medium)'
              e.currentTarget.style.transform = 'translateY(0)'
            }}
            onFocus={(e) => {
              e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
            }}
            onBlur={(e) => {
              e.currentTarget.style.boxShadow = 'none'
            }}
            aria-label="Learn more about how Care Connector works"
          >
            Learn More
          </Link>
        </div>
      </section>

      {/* Trust Indicators Section - Clean Apple Mac Desktop Style */}
      <section className="py-16 px-4 sm:px-8" style={{
        backgroundColor: 'var(--bg-secondary)',
        borderBottom: '1px solid var(--border-light)'
      }}>


        <div className="max-w-5xl mx-auto">
          {/* Section Header - Clean Apple Mac Desktop Typography */}
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-light mb-6" style={{
              color: 'var(--text-primary)',
              lineHeight: '1.15',
              fontWeight: '200',
              letterSpacing: '-0.01em'
            }}>
              Trusted by <span className="font-semibold" style={{
                fontWeight: '700',
                color: 'var(--primary)'
              }}>{homepageStats.verifiedProfessionals ? `${homepageStats.verifiedProfessionals.toLocaleString()}+` : '2,500+'}</span> Care Providers
            </h2>
            <p className="text-lg sm:text-xl max-w-3xl mx-auto" style={{
              color: 'var(--text-secondary)',
              lineHeight: '1.4',
              fontWeight: '300'
            }}>
              Join a growing community of healthcare professionals and families who trust our platform for comprehensive care coordination
            </p>
          </div>

          {/* Clean Stats Grid - Apple Mac Desktop Style */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-12">
            {homepageStats.verifiedProfessionals !== undefined ? (
              <div className="text-center p-6 rounded-xl transition-all duration-200 hover:shadow-md" style={{
                backgroundColor: 'var(--bg-primary)',
                border: '1px solid var(--border-light)',
                boxShadow: 'var(--shadow-card)'
              }}>
                <div className="text-3xl font-light mb-2" style={{color: 'var(--text-primary)'}}>
                  {homepageStats.verifiedProfessionals.toLocaleString()}
                </div>
                <div className="text-sm font-medium" style={{color: 'var(--text-secondary)'}}>Verified Professionals</div>
              </div>
            ) : (
              <div className="text-center p-6 rounded-xl animate-pulse" style={{
                backgroundColor: 'var(--bg-secondary)',
                border: '1px solid var(--border-light)'
              }}>
                <div className="h-8 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
              </div>
            )}
            {homepageStats.averageRating ? (
              <div className="text-center p-6 rounded-xl transition-all duration-200 hover:shadow-md" style={{
                backgroundColor: 'var(--bg-primary)',
                border: '1px solid var(--border-light)',
                boxShadow: 'var(--shadow-card)'
              }}>
                <div className="text-3xl font-light mb-2 flex items-center justify-center gap-1" style={{color: 'var(--text-primary)'}}>
                  {homepageStats.averageRating}
                  <Award className="w-6 h-6" style={{color: 'var(--accent-warning)'}} />
                </div>
                <div className="text-sm font-medium" style={{color: 'var(--text-secondary)'}}>Average Rating</div>
              </div>
            ) : (
              <div className="text-center p-6 rounded-xl animate-pulse" style={{
                backgroundColor: 'var(--bg-secondary)',
                border: '1px solid var(--border-light)'
              }}>
                <div className="h-8 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
              </div>
            )}
            {homepageStats.successfulBookings !== undefined ? (
              <div className="text-center p-6 rounded-xl transition-all duration-200 hover:shadow-md" style={{
                backgroundColor: 'var(--bg-primary)',
                border: '1px solid var(--border-light)',
                boxShadow: 'var(--shadow-card)'
              }}>
                <div className="text-3xl font-light mb-2" style={{color: 'var(--text-primary)'}}>
                  {homepageStats.successfulBookings.toLocaleString()}
                </div>
                <div className="text-sm font-medium" style={{color: 'var(--text-secondary)'}}>Successful Bookings</div>
              </div>
            ) : (
              <div className="text-center p-6 rounded-xl animate-pulse" style={{
                backgroundColor: 'var(--bg-secondary)',
                border: '1px solid var(--border-light)'
              }}>
                <div className="h-8 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
              </div>
            )}
            {homepageStats.supportStatus ? (
              <div className="text-center p-6 rounded-xl transition-all duration-200 hover:shadow-md" style={{
                backgroundColor: 'var(--bg-primary)',
                border: '1px solid var(--border-light)',
                boxShadow: 'var(--shadow-card)'
              }}>
                <div className="text-3xl font-light mb-2" style={{color: 'var(--text-primary)'}}>
                  {homepageStats.supportStatus}
                </div>
                <div className="text-sm font-medium" style={{color: 'var(--text-secondary)'}}>Support Available</div>
              </div>
            ) : (
              <div className="text-center p-6 rounded-xl animate-pulse" style={{
                backgroundColor: 'var(--bg-secondary)',
                border: '1px solid var(--border-light)'
              }}>
                <div className="h-8 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
              </div>
            )}
          </div>

          {/* Clean Security Badges - Apple Mac Desktop Style */}
          <div className="flex flex-wrap items-center justify-center gap-3 sm:gap-4">
            {securityBadges.map((badge) => {
              // Dynamic icon mapping based on icon string from database
              const iconMap = {
                'CheckCircle': CheckCircle,
                'Shield': Shield,
                'Award': Award
              }
              const IconComponent = iconMap[badge.icon] || CheckCircle

              return (
                <div
                  key={badge.id}
                  className="flex items-center gap-2 px-4 py-3 rounded-xl transition-all duration-200 hover:shadow-md cursor-default"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    border: '1px solid var(--border-light)',
                    boxShadow: 'var(--shadow-card)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-1px)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-hover)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                  }}
                >
                  <IconComponent className="w-5 h-5" style={{color: 'var(--primary)'}} />
                  <span className="text-sm font-medium whitespace-nowrap" style={{color: 'var(--text-primary)'}}>{badge.text}</span>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Search Section - Clean Apple Mac Desktop Style */}
      <section className="py-16 px-4 sm:px-8" style={{backgroundColor: 'var(--bg-primary)'}}>
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-light mb-4" style={{
              color: 'var(--text-primary)',
              lineHeight: '1.2'
            }}>
              {searchContent.title}
            </h2>
            <p className="text-base sm:text-lg max-w-xl mx-auto" style={{
              color: 'var(--text-secondary)',
              lineHeight: '1.5'
            }}>
              {searchContent.subtitle}
            </p>
          </div>

          {/* Display search form error if exists */}
          {error && (
            <div className="mb-6 p-4 rounded-lg" style={{backgroundColor: 'var(--bg-error)', border: '1px solid var(--border-error)'}} role="alert" aria-live="polite">
              <div className="flex items-center gap-2">
                <svg className="w-5 h-5 flex-shrink-0" style={{color: 'var(--text-error)'}} fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <span className="text-sm font-medium" style={{color: 'var(--text-error)'}}>{error}</span>
              </div>
            </div>
          )}

          <form onSubmit={(e) => { e.preventDefault(); handleSearchSubmit(); }} className="rounded-xl p-6 sm:p-8" style={{backgroundColor: 'var(--bg-secondary)', border: '1px solid var(--border-light)', boxShadow: 'var(--shadow-card)'}} role="search" aria-label="Find care providers" noValidate>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6">
              <div>
                <label htmlFor="care-type-select" className="block text-sm font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                  Care Type
                </label>
                <select
                  value={searchFilters.careType}
                  onChange={(e) => handleFilterChange('careType', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-opacity-20 transition-all duration-200"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  } as React.CSSProperties}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-medium)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                  }}
                  id="care-type-select"
                >
                  <option value="all">All Types</option>
                  {searchFilterOptions.careTypes.map(careType => (
                    <option key={careType.value} value={careType.value}>
                      {careType.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="location-input" className="block text-sm font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                  Location <span style={{color: 'var(--text-error)'}} aria-label="required">*</span>
                </label>
                <input
                  id="location-input"
                  type="text"
                  placeholder="City, state, or zip"
                  value={searchFilters.location}
                  onChange={(e) => {
                    handleFilterChange('location', e.target.value)
                    // Clear error when user starts typing
                    if (error && e.target.value.trim()) {
                      setError(null)
                      e.target.style.borderColor = 'var(--border-light)'
                    }
                  }}
                  required
                  aria-required="true"
                  aria-describedby="location-help"
                  className="w-full px-4 py-3 rounded-lg border transition-all duration-200 placeholder-styled focus:outline-none focus:ring-2 focus:ring-opacity-20"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  } as React.CSSProperties}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    if (!error) {
                      e.currentTarget.style.borderColor = 'var(--border-light)'
                      e.currentTarget.style.boxShadow = 'none'
                    }
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-medium)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                  }}
                />
                <div id="location-help" className="text-xs mt-1" style={{color: 'var(--text-muted)'}}>
                  {searchContent.locationHelp}
                </div>
              </div>

              <div>
                <label htmlFor="availability-select" className="block text-sm font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                  Availability
                </label>
                <select
                  id="availability-select"
                  value={searchFilters.availability}
                  onChange={(e) => handleFilterChange('availability', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-opacity-20 transition-all duration-200"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  } as React.CSSProperties}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-medium)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                  }}
                >
                  <option value="any">When do you need care?</option>
                  {searchFilterOptions.availability.map(avail => (
                    <option key={avail.value} value={avail.value}>
                      {avail.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="insurance-select" className="block text-sm font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                  Insurance Accepted
                </label>
                <select
                  id="insurance-select"
                  value={searchFilters.insurance}
                  onChange={(e) => handleFilterChange('insurance', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-opacity-20 transition-all duration-200"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  } as React.CSSProperties}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-medium)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                  }}
                >
                  <option value="any">Select your insurance</option>
                  {searchFilterOptions.insurance.map(ins => (
                    <option key={ins.value} value={ins.value}>
                      {ins.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="language-select" className="block text-sm font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                  Languages Spoken
                </label>
                <select
                  id="language-select"
                  value={searchFilters.language}
                  onChange={(e) => handleFilterChange('language', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-opacity-20 transition-all duration-200"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  } as React.CSSProperties}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-medium)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                  }}
                >
                  <option value="any">Preferred language</option>
                  {searchFilterOptions.languages.map(lang => (
                    <option key={lang.value} value={lang.value}>
                      {lang.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="certification-select" className="block text-sm font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                  Certifications
                </label>
                <select
                  id="certification-select"
                  value={searchFilters.certification}
                  onChange={(e) => handleFilterChange('certification', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-opacity-20 transition-all duration-200"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  } as React.CSSProperties}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-medium)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                  }}
                >
                  <option value="any">Required certifications</option>
                  {searchFilterOptions.certifications.map(cert => (
                    <option key={cert.value} value={cert.value}>
                      {cert.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="text-center">
              <button
                onClick={(e) => { e.preventDefault(); handleSearchSubmit(); }}
                disabled={loading}
                className="w-full px-6 py-3 rounded-lg text-base font-medium focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                style={{
                  backgroundColor: loading ? 'var(--text-muted)' : 'var(--primary)',
                  color: 'var(--bg-primary)',
                  border: 'none'
                } as React.CSSProperties}
                onMouseEnter={(e) => {
                  if (!loading) {
                    e.currentTarget.style.backgroundColor = 'var(--primary-dark)'
                  }
                }}
                onMouseLeave={(e) => {
                  if (!loading) {
                    e.currentTarget.style.backgroundColor = 'var(--primary)'
                  }
                }}
                onFocus={(e) => {
                  e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                }}
                onBlur={(e) => {
                  e.currentTarget.style.boxShadow = 'none'
                }}
                aria-label={loading ? "Searching for care providers..." : "Search for care providers"}
                aria-describedby="search-help"
                type="submit"
              >
                {loading ? (
                  <>
                    <div className="w-5 h-5 inline mr-3 border-2 border-white border-t-transparent rounded-full animate-spin" aria-hidden="true"></div>
                    Searching...
                  </>
                ) : (
                  <>
                    <Search className="w-5 h-5 inline mr-3" aria-hidden="true" />
                    Search Care Providers
                  </>
                )}
              </button>
              <div id="search-help" className="text-xs mt-2" style={{color: 'var(--text-muted)'}}>
                Find verified healthcare professionals in your area
              </div>
            </div>
          </form>
        </div>
      </section>

      {/* Features Section - Clean Apple Mac Desktop Style */}
      <section
        className="py-16 px-4 sm:px-8 text-center"
        style={{ backgroundColor: 'var(--bg-primary)' }}
      >
        <div className="max-w-5xl mx-auto">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-light mb-4" style={{
            color: 'var(--text-primary)',
            lineHeight: '1.2'
          }}>
            Your Complete Care Network
          </h2>
          <p className="text-base sm:text-lg mb-12 max-w-2xl mx-auto" style={{
            color: 'var(--text-secondary)',
            lineHeight: '1.5'
          }}>
            Healthcare coordination for modern families with comprehensive tools and verified professionals.
          </p>
        </div>

        <div className="max-w-5xl mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Find Verified Care */}
          <div
            className="rounded-xl p-6 border transition-all duration-300 hover:shadow-lg"
            style={{
              backgroundColor: 'var(--bg-secondary)',
              borderColor: 'var(--border-light)',
              color: 'var(--text-primary)',
              boxShadow: 'var(--shadow-card)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)'
              e.currentTarget.style.transform = 'translateY(-2px)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.boxShadow = 'var(--shadow-card)'
              e.currentTarget.style.transform = 'translateY(0)'
            }}
          >
            <div
              className="w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4"
              style={{ backgroundColor: 'var(--primary)' }}
            >
              <Search className="w-6 h-6" style={{ color: 'var(--bg-primary)' }} />
            </div>
            <h3 className="text-lg font-semibold mb-3" style={{ color: 'var(--text-primary)' }}>
              Find Verified Care
            </h3>
            <p className="mb-4 text-sm" style={{ color: 'var(--text-secondary)' }}>
              Connect with verified healthcare professionals who deliver exceptional, compassionate care.
            </p>
            <button
              onClick={handleBrowseProviders}
              className="w-full px-4 py-2 rounded-md font-medium flex items-center justify-center gap-2 transition-colors duration-150"
              style={{
                backgroundColor: 'var(--primary)',
                color: 'var(--bg-primary)'
              } as React.CSSProperties}
              onMouseEnter={(e) => {
                e.currentTarget.style.opacity = '0.9'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.opacity = '1'
              }}
            >
              Browse Providers
              <Search className="w-4 h-4" />
            </button>
          </div>

          {featuresContent.map((feature) => {
            // Dynamic icon mapping based on icon string from database
            const iconMap = {
              'Users': Users,
              'Sparkles': Sparkles
            }
            const IconComponent = iconMap[feature.icon] || Users
            
            // Dynamic action handlers
            const handleAction = () => {
              if (feature.action === 'create-group') {
                navigate('/create-group')
              } else if (feature.action === 'ai-helper') {
                handleAIHelper()
              }
            }

            return (
              <div
                key={feature.id}
                className="rounded-xl p-8 border transition-all duration-300 hover:shadow-lg"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  borderColor: 'var(--border-light)',
                  color: 'var(--text-primary)',
                  boxShadow: 'var(--shadow-card)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)'
                  e.currentTarget.style.transform = 'translateY(-4px)'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                  e.currentTarget.style.transform = 'translateY(0)'
                }}
              >
                <div
                  className="w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 transition-all duration-300"
                  style={{ backgroundColor: 'var(--primary)' }}
                >
                  <IconComponent className="w-8 h-8" style={{ color: 'var(--bg-primary)' }} />
                </div>
                <h3 className="text-xl font-semibold mb-4" style={{ 
                  color: 'var(--text-primary)', 
                  fontWeight: '600',
                  letterSpacing: '-0.01em' 
                }}>
                  {feature.title}
                </h3>
                <p className="mb-6 text-base leading-relaxed" style={{ 
                  color: 'var(--text-secondary)',
                  lineHeight: '1.6'
                }}>
                  {feature.description}
                </p>
                <button
                  onClick={handleAction}
                  className="w-full px-6 py-3 rounded-lg font-medium flex items-center justify-center gap-3 transition-all duration-300 shadow-sm hover:shadow-md"
                  style={{
                    backgroundColor: 'var(--primary)',
                    color: 'var(--bg-primary)',
                    fontWeight: '500',
                    letterSpacing: '-0.005em'
                  } as React.CSSProperties}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.opacity = '0.95'
                    e.currentTarget.style.transform = 'translateY(-1px)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.opacity = '1'
                    e.currentTarget.style.transform = 'translateY(0)'
                  }}
                >
                  {feature.buttonText}
                  {feature.action === 'create-group' && <Users className="w-5 h-5" />}
                </button>
              </div>
            )
          })}
        </div>
      </section>

      {/* Essential Care Tools */}
      <section className="py-20 px-8 text-center macos-section-elegant" style={{backgroundColor: 'var(--bg-secondary)'}}>
        <div className="max-w-4xl mx-auto mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-light mb-6" style={{
            color: 'var(--text-primary)',
            fontWeight: '200',
            letterSpacing: '-0.01em',
            lineHeight: '1.15'
          }}>
            {essentialToolsContent.title}
          </h2>
          <p className="text-lg sm:text-xl leading-relaxed" style={{
            color: 'var(--text-secondary)',
            fontWeight: '300',
            lineHeight: '1.4'
          }}>
            {essentialToolsContent.description}
          </p>
        </div>

        <div className="max-w-6xl mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
          {essentialToolsContent.tools.map((tool) => {
            // Dynamic icon mapping based on icon string from database
            const iconMap = {
              'Calendar': Calendar,
              'MessageSquare': MessageSquare,
              'CheckSquare': CheckSquare
            }
            const IconComponent = iconMap[tool.icon] || Calendar

            return (
              <Link
                key={tool.id}
                to={tool.route}
                className="rounded-xl p-8 shadow-sm block macos-card-elegant border transition-all duration-300 hover:shadow-lg"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  borderColor: 'var(--border-light)',
                  boxShadow: 'var(--shadow-card)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)'
                  e.currentTarget.style.transform = 'translateY(-4px)'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                  e.currentTarget.style.transform = 'translateY(0)'
                }}
              >
                <div
                  className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6"
                  style={{backgroundColor: 'var(--bg-accent)'}}
                >
                  <IconComponent className="w-8 h-8" style={{color: 'var(--primary)'}} />
                </div>
                <h3 className="text-xl font-semibold mb-4" style={{color: 'var(--text-primary)'}}>
                  {tool.name}
                </h3>
              </Link>
            )
          })}
        </div>
      </section>

      {/* Provider Cards Section - Clean Apple Mac Desktop Style */}
      <section className="py-16" style={{backgroundColor: 'var(--bg-secondary)'}}>
        <div className="max-w-5xl mx-auto px-6">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-light mb-4 text-center" style={{
            color: 'var(--text-primary)',
            lineHeight: '1.2'
          }}>
            {featuredProvidersContent.title}
          </h2>
          <p className="text-center mb-12 text-base sm:text-lg" style={{
            color: 'var(--text-secondary)',
            lineHeight: '1.5'
          }}>
            {featuredProvidersContent.description}
          </p>

          {loading ? (
            <div className="text-center py-16" role="status" aria-live="polite" aria-label="Loading care providers">
              <div className="max-w-sm mx-auto">
                <div className="flex justify-center mb-6">
                  <div className="relative">
                    <div className="w-12 h-12 rounded-full border-2 border-transparent animate-spin" style={{borderTopColor: 'var(--primary)', borderRightColor: 'var(--primary)'}} aria-hidden="true"></div>
                    <div className="absolute inset-0 w-12 h-12 rounded-full border-2 opacity-20" style={{borderColor: 'var(--primary)'}} aria-hidden="true"></div>
                  </div>
                </div>
                <h3 className="text-lg font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                  Connecting to Provider Network
                </h3>
                <p className="text-sm mb-3" style={{color: 'var(--text-secondary)'}}>
                  Searching verified healthcare professionals in your area...
                </p>
                <div className="flex items-center justify-center gap-2 text-xs animate-pulse-subtle" style={{color: 'var(--text-muted)'}}>
                  <span className="w-1 h-1 rounded-full" style={{backgroundColor: 'var(--primary)'}}></span>
                  <span>Verifying credentials</span>
                  <span className="w-1 h-1 rounded-full" style={{backgroundColor: 'var(--primary)'}}></span>
                  <span>Checking availability</span>
                  <span className="w-1 h-1 rounded-full" style={{backgroundColor: 'var(--primary)'}}></span>
                  <span>Matching preferences</span>
                </div>
                <div className="mt-4 text-xs" style={{color: 'var(--text-muted)'}}>
                  This may take a few moments...
                </div>
              </div>
            </div>
          ) : error ? (
            <div className="text-center py-16" role="alert" aria-live="assertive">
              <div className="max-w-md mx-auto">
                <div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6" style={{backgroundColor: 'var(--bg-error-light)'}}>
                  <svg className="w-8 h-8" style={{color: 'var(--text-error)'}} fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-3" style={{color: 'var(--text-primary)'}}>
                  Unable to Load Providers
                </h3>
                <p className="text-base mb-6" style={{color: 'var(--text-secondary)'}}>
                  {error}
                </p>
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <button
                    onClick={() => {
                      setError(null)
                      setLoading(true)
                      // Retry data fetching
                      const fetchData = async () => {
                        try {
                          const [caregiversData, statsData, searchOptionsData, navigationOptionsData] = await Promise.all([
                            dataService.getCaregivers(),
                            dataService.getHomepageStats(),
                            dataService.getSearchFilterOptions(),
                            dataService.getNavigationOptions()
                          ])
                          setCaregivers(caregiversData.slice(0, 3))
                          setHomepageStats(statsData)
                          setSearchFilterOptions(searchOptionsData)
                          setNavigationOptions(navigationOptionsData)
                        } catch (error) {
                          console.error('Retry failed:', error)
                          setError('Failed to load care providers. Please check your connection and try again.')
                        } finally {
                          setLoading(false)
                        }
                      }
                      fetchData()
                    }}
                    className="button-primary px-6 py-3 rounded-lg font-medium transition-all duration-200"
                    style={{
                      backgroundColor: 'var(--primary)',
                      color: 'var(--bg-primary)'
                    }}
                    aria-label="Retry loading care providers"
                  >
                    Try Again
                  </button>
                  <button
                    onClick={() => navigate('/caregivers')}
                    className="button-secondary px-6 py-3 rounded-lg font-medium transition-all duration-200"
                    style={{
                      backgroundColor: 'var(--bg-secondary)',
                      color: 'var(--text-primary)',
                      border: '1px solid var(--border-medium)'
                    }}
                    aria-label="Browse all care providers"
                  >
                    Browse All Providers
                  </button>
                </div>
              </div>
            </div>
          ) : caregivers.length > 0 ? (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {caregivers.map((caregiver) => (
                  <div
                    key={caregiver.id}
                    className="rounded-xl p-6 transition-all duration-300 border cursor-pointer hover:shadow-lg"
                    style={{
                      backgroundColor: 'var(--bg-primary)',
                      borderColor: 'var(--border-light)',
                      boxShadow: 'var(--shadow-card)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)'
                      e.currentTarget.style.transform = 'translateY(-4px)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                      e.currentTarget.style.transform = 'translateY(0)'
                    }}
                  >
                    {/* Header with Avatar and Verification */}
                    <div className="flex items-start gap-3 mb-4">
                      <div className="relative">
                        <div
                          className="w-12 h-12 rounded-full flex items-center justify-center"
                          style={{backgroundColor: 'var(--bg-accent)'}}
                        >
                          {caregiver.avatar_url ? (
                            <img
                              src={caregiver.avatar_url}
                              alt={caregiver.full_name || 'Provider'}
                              className="w-12 h-12 rounded-full object-cover"
                            />
                          ) : (
                            <Users className="w-6 h-6" style={{color: 'var(--text-secondary)'}} />
                          )}
                        </div>
                        {/* Verification Badge */}
                        <div className="absolute -top-0.5 -right-0.5 w-4 h-4 rounded-full flex items-center justify-center" style={{backgroundColor: 'var(--primary)'}}>
                          <Shield className="w-2.5 h-2.5" style={{color: 'var(--bg-primary)'}} />
                        </div>
                      </div>

                      <div className="flex-1">
                        <h3 className="font-semibold text-lg mb-1" style={{color: 'var(--text-primary)'}}>
                          {caregiver.full_name || 'Professional Caregiver'}
                        </h3>

                        {/* Rating Display */}
                        <div className="flex items-center gap-2 mb-2">
                          <div className="flex items-center">
                            {caregiver.average_rating ? [1, 2, 3, 4, 5].map((star) => (
                              <span key={star} className="text-sm" style={{color: star <= caregiver.average_rating ? 'var(--accent-warning)' : 'var(--text-muted)'}}>
                                ★
                              </span>
                            )) : (
                              <span className="text-sm" style={{color: 'var(--text-muted)'}}>Not rated</span>
                            )}
                          </div>
                          <span className="text-sm font-medium" style={{color: 'var(--text-secondary)'}}>
                            {caregiver.average_rating ? `${caregiver.average_rating.toFixed(1)}★` : 'New provider'} {caregiver.reviews_count > 0 && `(${caregiver.reviews_count} reviews)`}
                          </span>
                        </div>

                        {/* Availability Indicator */}
                        {caregiver.availability_status && (
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 rounded-full" style={{backgroundColor: caregiver.availability_status === 'available' ? 'var(--success)' : 'var(--text-muted)'}}></div>
                            <span className="text-sm font-medium" style={{color: caregiver.availability_status === 'available' ? 'var(--success)' : 'var(--text-muted)'}}>
                              {caregiver.availability_status === 'available' ? 'Available' : 'Busy'}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Bio */}
                    {caregiver.bio && (
                      <p className="text-sm mb-4 leading-relaxed" style={{color: 'var(--text-secondary)'}}>
                        {caregiver.bio.length > 120 ? `${caregiver.bio.substring(0, 120)}...` : caregiver.bio}
                      </p>
                    )}
                    <div className="text-center mb-4">
                      {caregiver.location && (
                        <div className="flex items-center justify-center gap-1 mb-2" style={{color: 'var(--text-secondary)'}}>
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                            <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                          </svg>
                          <span>{caregiver.location}</span>
                        </div>
                      )}
                      {caregiver.specialties && (
                        <div className="flex gap-2 justify-center flex-wrap">
                          {caregiver.specialties.slice(0, 2).map((specialty: string, index: number) => (
                            <span
                              key={index}
                              className="px-2 py-1 rounded text-sm"
                              style={{
                                backgroundColor: 'var(--bg-accent)',
                                color: 'var(--text-primary)'
                              }}
                            >
                              {specialty}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                    <Link
                      to={`/provider/caregiver/${caregiver.id}`}
                      className="button-primary w-full py-2 px-4 rounded-lg font-medium transition-colors block text-center hover:opacity-90"
                    >
                      View Profile
                    </Link>
                  </div>
                ))}
              </div>

              <div className="text-center mt-8">
                <Link
                  to="/caregivers"
                  className="button-primary px-6 py-3 rounded-lg font-medium transition-colors inline-block hover:opacity-90"
                >
                  View All Caregivers →
                </Link>
              </div>
            </>
          ) : (
            <div className="text-center py-16">
              <div className="max-w-md mx-auto">
                <div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6" style={{backgroundColor: 'var(--bg-accent)'}}>
                  <Search className="w-8 h-8" style={{color: 'var(--primary)'}} />
                </div>
                <h3 className="text-xl font-semibold mb-3" style={{color: 'var(--text-primary)'}}>
                  {providerNetworkContent.title}
                </h3>
                <p className="text-base mb-6" style={{color: 'var(--text-secondary)'}}>
                  {providerNetworkContent.description}
                </p>
                <button
                  onClick={handleBrowseProviders}
                  className="button-primary px-6 py-3 rounded-lg font-medium macos-button-elegant"
                >
                  {providerNetworkContent.buttonText}
                </button>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Take Control Section */}
      <section className="py-16 sm:py-20 px-4 sm:px-8 text-center" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <h2 className="text-3xl sm:text-4xl lg:text-5xl font-light mb-6" style={{
          color: 'var(--text-primary)',
          lineHeight: '1.2'
        }}>
          {takeControlContent.title}
        </h2>
        <p className="mb-8 text-base sm:text-lg max-w-2xl mx-auto" style={{
          color: 'var(--text-secondary)',
          lineHeight: '1.5'
        }}>
          {takeControlContent.description}
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <button
            onClick={handleGetStarted}
            className="button-primary px-6 py-3 rounded-lg font-medium transition-colors hover:opacity-90"
          >
            Get Started →
          </button>

          <button
            onClick={handleBrowseProviders}
            className="button-secondary px-6 py-3 rounded-lg font-medium flex items-center gap-2 macos-button-elegant"
          >
            <Search className="w-4 h-4" />
            Browse Providers
          </button>
        </div>
      </section>

      {/* Footer - Enhanced Spacing & Alignment */}
      <footer
        className="py-12 sm:py-16 lg:py-20 px-4 sm:px-8 border-t"
        style={{
          backgroundColor: 'var(--bg-primary)',
          borderColor: 'var(--border-light)'
        }}
      >
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 sm:gap-12 mb-8 sm:mb-12">
            {/* Logo and Description - Enhanced */}
            <div className="lg:col-span-1 text-center sm:text-left">
              <div className="flex items-center justify-center sm:justify-start gap-3 mb-6">
                <div
                  className="w-10 h-10 rounded-xl flex items-center justify-center shadow-sm"
                  style={{backgroundColor: 'var(--primary)'}}
                >
                  <span className="font-bold text-base" style={{color: 'var(--bg-primary)'}}>CC</span>
                </div>
                <span className="font-bold text-xl sm:text-2xl" style={{color: 'var(--text-primary)'}}>Care Connector</span>
              </div>
              <p className="mb-6 text-sm sm:text-base leading-relaxed max-w-md mx-auto sm:mx-0" style={{color: 'var(--text-secondary)'}}>
                {footerContent.description}
              </p>
              <div className="flex flex-col sm:flex-row flex-wrap gap-4 sm:gap-6 justify-center sm:justify-start">
                <div className="flex items-center gap-2 text-sm" style={{color: 'var(--text-secondary)'}}>
                  <Shield className="w-4 h-4" style={{color: 'var(--primary)'}} />
                  <span className="font-medium">HIPAA Compliant</span>
                </div>
                <div className="flex items-center gap-2 text-sm" style={{color: 'var(--text-secondary)'}}>
                  <UserCheck className="w-4 h-4" style={{color: 'var(--primary)'}} />
                  <span className="font-medium">Verified Providers</span>
                </div>
              </div>
            </div>

            {/* Services - Enhanced */}
            <div className="text-center sm:text-left">
              <h3 className="font-semibold mb-4 sm:mb-6 text-base sm:text-lg" style={{ color: 'var(--text-primary)' }}>{footerContent.servicesTitle}</h3>
              <ul className="space-y-4">
                <li>
                  <Link
                    to="/caregivers"
                    className="transition-all duration-200 hover:translate-x-1 focus:outline-none focus:ring-2 focus:ring-opacity-50 rounded-sm px-1 py-0.5"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                    onFocus={(e) => {
                      e.currentTarget.style.color = 'var(--primary)'
                      e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.color = 'var(--text-secondary)'
                      e.currentTarget.style.boxShadow = 'none'
                    }}
                  >
                    Find Care
                  </Link>
                </li>
                <li>
                  <Link
                    to="/care-groups"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    Care Groups
                  </Link>
                </li>
              </ul>
            </div>

            {/* Learn More - Enhanced */}
            <div className="text-center sm:text-left">
              <h3 className="font-semibold mb-4 sm:mb-6 text-base sm:text-lg" style={{ color: 'var(--text-primary)' }}>{footerContent.learnMoreTitle}</h3>
              <ul className="space-y-4">
                <li>
                  <Link
                    to="/how-it-works"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    How It Works
                  </Link>
                </li>
                <li>
                  <Link
                    to="/features"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    Features
                  </Link>
                </li>
              </ul>
            </div>

            {/* Legal & Compliance - New Section */}
            <div className="text-center sm:text-left">
              <h3 className="font-semibold mb-4 sm:mb-6 text-base sm:text-lg" style={{ color: 'var(--text-primary)' }}>Legal & Compliance</h3>
              <ul className="space-y-4">
                <li>
                  <Link
                    to="/privacy-policy"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link
                    to="/terms-of-service"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link
                    to="/hipaa-notice"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    HIPAA Notice
                  </Link>
                </li>
                <li>
                  <Link
                    to="/provider-portal"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    Provider Portal
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div
            className="pt-6 sm:pt-8 text-center border-t"
            style={{
              borderColor: 'var(--border-light)',
              color: 'var(--text-secondary)'
            }}
          >
            <p className="text-xs sm:text-sm">© {new Date().getFullYear()} Care Connector. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </main>
  )
}
