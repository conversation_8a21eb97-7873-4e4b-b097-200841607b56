import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Search, MapPin, Star, Clock, Users, Heart, MessageCircle } from 'lucide-react'
import { dataService } from '../lib/dataService'
import { supabase } from '../lib/supabase'

interface Companion {
  id: string
  full_name: string
  bio: string
  location: string
  specialties: string[]
  avatar_url?: string
  average_rating?: number
  reviews_count?: number
  years_of_experience?: number
  hourly_rate?: number
  availability_status?: string
}

export default function Companions() {
  const navigate = useNavigate()
  const [companions, setCompanions] = useState<Companion[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [locationFilter, setLocationFilter] = useState('')
  const [specialtyFilter, setSpecialtyFilter] = useState('')
  const [sortBy, setSortBy] = useState('name')
  const [favorites, setFavorites] = useState<string[]>([])

  const toggleFavorite = (companionId: string) => {
    setFavorites(prev =>
      prev.includes(companionId)
        ? prev.filter(id => id !== companionId)
        : [...prev, companionId]
    )
  }

  const handleSearch = async () => {
    console.log('Search triggered with:', { searchTerm, locationFilter, specialtyFilter })
    setLoading(true)
    setError(null)

    try {
      // Validate search inputs
      if (searchTerm.trim().length > 0 && searchTerm.trim().length < 2) {
        throw new Error('Search term must be at least 2 characters long')
      }
      // Build search query with filters
      let query = supabase
        .schema('care_connector')
        .from('profiles')
        .select(`
          id,
          full_name,
          role,
          location,
          hourly_rate,
          rating,
          avatar_url,
          bio,
          specialties,
          availability_status,
          years_experience,
          phone,
          email
        `)
        .eq('role', 'companion')

      // Apply search term filter
      if (searchTerm.trim()) {
        query = query.or(`full_name.ilike.%${searchTerm.trim()}%,bio.ilike.%${searchTerm.trim()}%`)
      }

      // Apply location filter
      if (locationFilter.trim()) {
        query = query.ilike('location', `%${locationFilter.trim()}%`)
      }

      // Apply specialty filter
      if (specialtyFilter) {
        query = query.contains('specialties', [specialtyFilter])
      }

      const { data, error: searchError } = await query.limit(100)

      if (searchError) {
        throw new Error(`Search failed: ${searchError.message}`)
      }

      // Transform data to match Companion interface
      const transformedData = (data || []).map((profile: any) => ({
        id: profile.id,
        name: profile.full_name || '',
        full_name: profile.full_name || '',
        location: profile.location || '',
        hourly_rate: profile.hourly_rate || 0,
        rating: profile.rating || 0,
        avatar_url: profile.avatar_url || '',
        bio: profile.bio || '',
        specialties: profile.specialties || [],
        availability_status: profile.availability_status || 'available',
        years_experience: profile.years_experience || 0,
        phone: profile.phone || '',
        email: profile.email || ''
      }))

      setCompanions(transformedData)
    } catch (error) {
      console.error('Search error:', error)
      setError('Search failed. Please try again.')
      setCompanions([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    const fetchCompanions = async () => {
      try {
        setLoading(true)
        setError(null)
        const data = await dataService.getCompanions()
        setCompanions(data)
      } catch (error) {
        console.error('Error fetching companions:', error)
        setError('Failed to load companions. Please try again.')
        setCompanions([])
      } finally {
        setLoading(false)
      }
    }

    fetchCompanions()
  }, [])

  // Auto-trigger search when filters change
  useEffect(() => {
    if (companions.length > 0) {
      const timeoutId = setTimeout(() => {
        handleSearch()
      }, 500)

      return () => clearTimeout(timeoutId)
    }
  }, [searchTerm, locationFilter, specialtyFilter, companions.length])

  // Use database search results directly (no client-side filtering needed)
  const filteredCompanions = companions

  const sortedCompanions = filteredCompanions.sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return (a.full_name || '').localeCompare(b.full_name || '')
      case 'rating':
        return (b.average_rating || 0) - (a.average_rating || 0)
      case 'experience':
        return (b.years_of_experience || 0) - (a.years_of_experience || 0)
      default:
        return 0
    }
  })

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
      {/* Header Section - Elegant & Clean */}
      <div className="px-8 py-16" style={{ backgroundColor: 'var(--bg-primary)', borderBottom: '1px solid var(--border-light)' }}>
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl font-light mb-6" style={{ color: 'var(--text-primary)' }}>Companions</h1>
          <p className="text-xl max-w-3xl mx-auto leading-relaxed" style={{ color: 'var(--text-secondary)' }}>Find trusted companions for companionship and daily support</p>
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className="px-8 py-6" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="max-w-6xl mx-auto">
          <form onSubmit={(e) => { e.preventDefault(); handleSearch(); }} className="flex flex-col md:flex-row gap-4 mb-6">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5" style={{ color: 'var(--text-muted)' }} />
              <input
                type="text"
                placeholder="Search companions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 rounded-lg border focus:outline-none focus:ring-2"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  borderColor: 'var(--border-medium)',
                  color: 'var(--text-primary)',
                  boxShadow: '0 0 0 3px var(--focus-shadow)'
                }}
              />
            </div>

            {/* Location Filter */}
            <div className="md:w-48">
              <input
                type="text"
                placeholder="Location"
                value={locationFilter}
                onChange={(e) => setLocationFilter(e.target.value)}
                className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  borderColor: 'var(--border-medium)',
                  color: 'var(--text-primary)'
                }}
              />
            </div>

            {/* Specialty Filter */}
            <div className="md:w-48">
              <input
                type="text"
                placeholder="Specialty"
                value={specialtyFilter}
                onChange={(e) => setSpecialtyFilter(e.target.value)}
                className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  borderColor: 'var(--border-medium)',
                  color: 'var(--text-primary)'
                }}
              />
            </div>

            {/* Sort By */}
            <div className="md:w-32">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  borderColor: 'var(--border-medium)',
                  color: 'var(--text-primary)'
                }}
              >
                <option value="name">Name</option>
                <option value="rating">Rating</option>
                <option value="experience">Experience</option>
              </select>
            </div>
          </form>

          {/* Results Count */}
          <div className="flex items-center justify-between mb-6">
            <p style={{ color: 'var(--text-secondary)' }}>
              {filteredCompanions.length} companion{filteredCompanions.length !== 1 ? 's' : ''} found
            </p>
          </div>
        </div>
      </div>

      {/* Companions Grid */}
      <div className="px-8 py-6">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-2xl font-bold mb-6" style={{ color: 'var(--text-primary)' }}>
            Available Companions
          </h2>
          {loading ? (
            <div className="text-center py-12">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2" style={{ borderColor: 'var(--primary)' }}></div>
              <p className="mt-4" style={{ color: 'var(--text-secondary)' }}>Loading companions...</p>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <div className="inline-flex items-center gap-3 px-8 py-6 rounded-xl shadow-lg" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
                <div className="w-8 h-8 rounded-full flex items-center justify-center" style={{ backgroundColor: 'var(--bg-accent)' }}>
                  <Search className="w-5 h-5" style={{ color: 'var(--primary)' }} />
                </div>
                <div>
                  <div className="text-xl font-semibold mb-1" style={{ color: 'var(--text-primary)' }}>Unable to Load Companions</div>
                  <div className="text-sm" style={{ color: 'var(--text-secondary)' }}>{error}</div>
                </div>
              </div>
            </div>
          ) : sortedCompanions.length === 0 ? (
            <div className="text-center py-12">
              <Users className="mx-auto mb-4 w-16 h-16" style={{ color: 'var(--text-muted)' }} />
              <h3 className="text-xl font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>No companions found</h3>
              <p style={{ color: 'var(--text-secondary)' }}>Try adjusting your search criteria or filters</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
              {sortedCompanions.map((companion) => (
                <div
                  key={companion.id}
                  className="group rounded-2xl p-6 transition-all duration-300 transform hover:scale-[1.02] hover:shadow-2xl animate-fadeInUp"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    border: '2px solid var(--border-light)',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
                  }}
                >
                  {/* Enhanced Profile Header */}
                  <div className="flex items-start mb-6">
                    <div className="flex-shrink-0 mr-5">
                      {companion.avatar_url ? (
                        <img
                          src={companion.avatar_url}
                          alt={companion.full_name || 'Companion'}
                          className="w-20 h-20 rounded-2xl object-cover shadow-lg ring-4 ring-accent group-hover:ring-primary transition-all duration-300"
                        />
                      ) : (
                        <div className="w-20 h-20 rounded-2xl flex items-center justify-center shadow-lg ring-4 ring-accent group-hover:ring-primary transition-all duration-300" style={{ backgroundColor: 'var(--bg-accent)' }}>
                          <Heart className="w-8 h-8" style={{ color: 'var(--primary)' }} />
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold mb-2 group-hover:text-primary transition-colors" style={{ color: 'var(--text-primary)' }}>
                        {companion.full_name || 'Companion'}
                      </h3>
                      <div className="flex items-center mb-3">
                        <MapPin className="w-4 h-4 mr-2" style={{ color: 'var(--text-muted)' }} />
                        <span className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
                          {companion.location || 'Location not specified'}
                        </span>
                      </div>
                      {/* Enhanced Rating Display */}
                      {companion.average_rating && (
                        <div className="flex items-center mb-3 p-2 rounded-lg" style={{ backgroundColor: 'var(--bg-accent)' }}>
                          <Star className="w-4 h-4 mr-2" style={{ color: 'var(--warning)', fill: 'var(--warning)' }} />
                          <span className="text-sm font-bold" style={{ color: 'var(--text-primary)' }}>
                            {companion.average_rating.toFixed(1)}
                          </span>
                          <span className="text-sm ml-1" style={{ color: 'var(--text-secondary)' }}>
                            ({companion.reviews_count || 0} reviews)
                          </span>
                        </div>
                      )}

                      {/* Experience and Rate */}
                      <div className="flex items-center justify-between text-sm" style={{ color: 'var(--text-secondary)' }}>
                        {companion.years_of_experience && (
                          <div className="flex items-center">
                            <Clock className="w-4 h-4 mr-1" />
                            <span>{companion.years_of_experience}+ years exp</span>
                          </div>
                        )}
                        {companion.hourly_rate && (
                          <div className="flex items-center">
                            <span>${companion.hourly_rate}/hr</span>
                          </div>
                        )}
                      </div>

                      {/* Availability Status */}
                      {companion.availability_status && (
                        <div className="flex items-center mt-2">
                          <div className="w-2 h-2 rounded-full mr-2" style={{ backgroundColor: companion.availability_status === 'available' ? 'var(--success)' : 'var(--text-muted)' }}></div>
                          <span className="text-sm" style={{ color: companion.availability_status === 'available' ? 'var(--success)' : 'var(--text-secondary)' }}>
                            {companion.availability_status === 'available' ? 'Available Today' : 'Contact for Availability'}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Bio */}
                  <p className="text-sm mb-4 line-clamp-3" style={{ color: 'var(--text-secondary)' }}>
                    {companion.bio || 'Dedicated companion providing emotional support and daily assistance.'}
                  </p>

                  {/* Specialties */}
                  {companion.specialties && companion.specialties.length > 0 && (
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-2">
                        {companion.specialties.slice(0, 3).map((specialty: string, index: number) => (
                          <span
                            key={index}
                            className="px-3 py-1 rounded-full text-xs font-medium"
                            style={{
                              backgroundColor: 'var(--bg-accent)',
                              color: 'var(--primary)'
                            }}
                          >
                            {specialty}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <button
                      className="button-primary flex-1 py-2 px-4 rounded-lg font-medium transition-colors hover:opacity-90"
                      onClick={() => navigate(`/provider/companions/${companion.id}`)}
                    >
                      View Profile
                    </button>
                    <button
                      className="button-secondary p-2 rounded-lg transition-colors hover:opacity-90"
                      style={{
                        color: favorites.includes(companion.id) ? 'var(--error)' : 'var(--text-primary)',
                        border: '1px solid var(--border-light)'
                      }}
                      onClick={() => toggleFavorite(companion.id)}
                      title={favorites.includes(companion.id) ? 'Remove from favorites' : 'Add to favorites'}
                    >
                      <Heart className={`w-4 h-4 ${favorites.includes(companion.id) ? 'fill-current' : ''}`} />
                    </button>
                    <button
                      className="p-2 rounded-lg transition-colors"
                      style={{
                        backgroundColor: 'var(--bg-secondary)',
                        color: 'var(--text-primary)',
                        border: '1px solid var(--border-medium)'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--bg-accent)'
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
                      }}
                      onClick={() => navigate('/messages', { state: { startConversationWith: companion.id, providerName: companion.full_name } })}
                      title={`Send message to ${companion.full_name}`}
                    >
                      <MessageCircle className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
