import React, { useState, useEffect } from 'react'
import { supabase } from '../lib/supabase'

interface Provider {
  id: string
  email: string
  full_name: string | null
  phone: string | null
  role: string
  location: string | null
  specialty: string | null
  experience_years: number | null
  hourly_rate: number | null
  availability_status: string | null
  profile_image_url: string | null
  bio: string | null
  rating: number | null
  verified: boolean
  created_at: string
  updated_at: string
}

interface ProviderStats {
  total: number
  caregivers: number
  companions: number
  careCheckers: number
  verified: number
  pending: number
}

const ProviderManagement: React.FC = () => {
  const [providers, setProviders] = useState<Provider[]>([])
  const [stats, setStats] = useState<ProviderStats>({
    total: 0,
    caregivers: 0,
    companions: 0,
    careCheckers: 0,
    verified: 0,
    pending: 0
  })
  const [loading, setLoading] = useState(true)
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null)
  const [showProviderModal, setShowProviderModal] = useState(false)
  const [filterRole, setFilterRole] = useState<string>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    loadProviders()
    loadStats()
  }, [])

  const loadProviders = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .in('role', ['caregiver', 'companion', 'care_checker'])
        .order('created_at', { ascending: false })

      if (error) throw error
      setProviders(data || [])
    } catch (error) {
      console.error('Error loading providers:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('role, verified')
        .in('role', ['caregiver', 'companion', 'care_checker'])

      if (error) throw error

      const statsData = data || []
      const newStats: ProviderStats = {
        total: statsData.length,
        caregivers: statsData.filter(p => p.role === 'caregiver').length,
        companions: statsData.filter(p => p.role === 'companion').length,
        careCheckers: statsData.filter(p => p.role === 'care_checker').length,
        verified: statsData.filter(p => p.verified).length,
        pending: statsData.filter(p => !p.verified).length
      }

      setStats(newStats)
    } catch (error) {
      console.error('Error loading provider stats:', error)
    }
  }

  const handleVerifyProvider = async (providerId: string, verified: boolean) => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ verified, updated_at: new Date().toISOString() })
        .eq('id', providerId)

      if (error) throw error

      // Refresh data
      await loadProviders()
      await loadStats()
    } catch (error) {
      console.error('Error updating provider verification:', error)
    }
  }

  const handleDeleteProvider = async (providerId: string) => {
    if (!confirm('Are you sure you want to delete this provider? This action cannot be undone.')) {
      return
    }

    try {
      const { error } = await supabase
        .from('profiles')
        .delete()
        .eq('id', providerId)

      if (error) throw error

      // Refresh data
      await loadProviders()
      await loadStats()
      setShowProviderModal(false)
    } catch (error) {
      console.error('Error deleting provider:', error)
    }
  }

  const filteredProviders = providers.filter(provider => {
    const matchesRole = filterRole === 'all' || provider.role === filterRole
    const matchesStatus = filterStatus === 'all' || 
      (filterStatus === 'verified' && provider.verified) ||
      (filterStatus === 'pending' && !provider.verified)
    const matchesSearch = !searchTerm || 
      provider.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      provider.email.toLowerCase().includes(searchTerm.toLowerCase())

    return matchesRole && matchesStatus && matchesSearch
  })

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-logo-green mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading provider management...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <h1 className="text-3xl font-bold text-gray-900">Provider Management</h1>
            <p className="mt-2 text-gray-600">Manage all care providers, verify accounts, and monitor performance</p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900">{stats.total}</div>
              <div className="text-sm text-gray-600 mt-1">Total Providers</div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-logo-green">{stats.caregivers}</div>
              <div className="text-sm text-gray-600 mt-1">Caregivers</div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-logo-green">{stats.companions}</div>
              <div className="text-sm text-gray-600 mt-1">Companions</div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-logo-green">{stats.careCheckers}</div>
              <div className="text-sm text-gray-600 mt-1">Care Checkers</div>
            </div>
          </div>
          
          <div className="rounded-lg p-6" style={{ backgroundColor: 'var(--bg-primary)', boxShadow: 'var(--shadow-card)' }}>
            <div className="text-center">
              <div className="text-3xl font-bold" style={{ color: 'var(--text-success)' }}>{stats.verified}</div>
              <div className="text-sm mt-1" style={{ color: 'var(--text-secondary)' }}>Verified</div>
            </div>
          </div>

          <div className="rounded-lg p-6" style={{ backgroundColor: 'var(--bg-primary)', boxShadow: 'var(--shadow-card)' }}>
            <div className="text-center">
              <div className="text-3xl font-bold" style={{ color: 'var(--text-warning)' }}>{stats.pending}</div>
              <div className="text-sm mt-1" style={{ color: 'var(--text-secondary)' }}>Pending</div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="rounded-lg mb-6" style={{ backgroundColor: 'var(--bg-primary)', boxShadow: 'var(--shadow-card)' }}>
          <div className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="Search providers by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-4 py-2 rounded-lg transition-all duration-200"
                  style={{
                    border: '1px solid var(--border-medium)',
                    backgroundColor: 'var(--bg-primary)',
                    color: 'var(--text-primary)'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-medium)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                />
              </div>
              
              <select
                value={filterRole}
                onChange={(e) => setFilterRole(e.target.value)}
                className="px-4 py-2 rounded-lg transition-all duration-200"
                style={{
                  border: '1px solid var(--border-medium)',
                  backgroundColor: 'var(--bg-primary)',
                  color: 'var(--text-primary)'
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = 'var(--primary)'
                  e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = 'var(--border-medium)'
                  e.currentTarget.style.boxShadow = 'none'
                }}
              >
                <option value="all">All Roles</option>
                <option value="caregiver">Caregivers</option>
                <option value="companion">Companions</option>
                <option value="care_checker">Care Checkers</option>
              </select>
              
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-4 py-2 rounded-lg transition-all duration-200"
                style={{
                  border: '1px solid var(--border-medium)',
                  backgroundColor: 'var(--bg-primary)',
                  color: 'var(--text-primary)'
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = 'var(--primary)'
                  e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = 'var(--border-medium)'
                  e.currentTarget.style.boxShadow = 'none'
                }}
              >
                <option value="all">All Status</option>
                <option value="verified">Verified</option>
                <option value="pending">Pending</option>
              </select>
            </div>
          </div>
        </div>

        {/* Providers Table */}
        <div className="rounded-lg overflow-hidden" style={{ backgroundColor: 'var(--bg-primary)', boxShadow: 'var(--shadow-card)' }}>
          <div className="overflow-x-auto">
            <table className="min-w-full" style={{ borderCollapse: 'separate', borderSpacing: '0' }}>
              <thead style={{ backgroundColor: 'var(--bg-secondary)' }}>
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: 'var(--text-secondary)' }}>
                    Provider
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Role
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rating
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Location
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Joined
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredProviders.map((provider) => (
                  <tr key={provider.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          {provider.profile_image_url ? (
                            <img
                              className="h-10 w-10 rounded-full object-cover"
                              src={provider.profile_image_url}
                              alt=""
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-full bg-logo-green text-white flex items-center justify-center text-sm font-medium">
                              {provider.full_name
                                ? provider.full_name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
                                : provider.email[0].toUpperCase()}
                            </div>
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {provider.full_name || 'Name not provided'}
                          </div>
                          <div className="text-sm text-gray-500">{provider.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-logo-green text-white capitalize">
                        {provider.role.replace('_', ' ')}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        provider.verified
                          ? 'bg-green-100 text-green-800'
                          : 'bg-orange-100 text-orange-800'
                      }`}>
                        {provider.verified ? 'Verified' : 'Pending'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {provider.rating ? (
                        <div className="flex items-center">
                          <span className="text-yellow-400">★</span>
                          <span className="ml-1">{provider.rating.toFixed(1)}</span>
                        </div>
                      ) : (
                        <span className="text-gray-400">No ratings</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {provider.location || 'Not specified'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(provider.created_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => {
                          setSelectedProvider(provider)
                          setShowProviderModal(true)
                        }}
                        className="text-logo-green hover:text-green-600 mr-4"
                      >
                        View Details
                      </button>
                      <button
                        onClick={() => handleVerifyProvider(provider.id, !provider.verified)}
                        className={`mr-4 ${
                          provider.verified
                            ? 'text-orange-600 hover:text-orange-700'
                            : 'text-green-600 hover:text-green-700'
                        }`}
                      >
                        {provider.verified ? 'Unverify' : 'Verify'}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredProviders.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-500">No providers match your current filters.</div>
            </div>
          )}
        </div>
      </div>

      {/* Provider Details Modal */}
      {showProviderModal && selectedProvider && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-lg bg-white">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Provider Details</h3>
              <button
                onClick={() => setShowProviderModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>

            <div className="space-y-6">
              <div className="flex items-center space-x-4">
                {selectedProvider.profile_image_url ? (
                  <img
                    className="h-20 w-20 rounded-full object-cover"
                    src={selectedProvider.profile_image_url}
                    alt=""
                  />
                ) : (
                  <div className="h-20 w-20 rounded-full bg-logo-green text-white flex items-center justify-center text-xl font-medium">
                    {selectedProvider.full_name
                      ? selectedProvider.full_name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
                      : selectedProvider.email[0].toUpperCase()}
                  </div>
                )}
                <div>
                  <h4 className="text-xl font-semibold text-gray-900">
                    {selectedProvider.full_name || 'Name not provided'}
                  </h4>
                  <p className="text-gray-600">{selectedProvider.email}</p>
                  <p className="text-sm text-gray-500 capitalize">
                    {selectedProvider.role.replace('_', ' ')}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Phone</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {selectedProvider.phone || 'Not provided'}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Location</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {selectedProvider.location || 'Not provided'}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Experience</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {selectedProvider.experience_years ? `${selectedProvider.experience_years} years` : 'Not specified'}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Hourly Rate</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {selectedProvider.hourly_rate ? `$${selectedProvider.hourly_rate}/hour` : 'Not set'}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Rating</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {selectedProvider.rating ? (
                      <span className="flex items-center">
                        <span className="text-yellow-400">★</span>
                        <span className="ml-1">{selectedProvider.rating.toFixed(1)}</span>
                      </span>
                    ) : (
                      'No ratings yet'
                    )}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Status</label>
                  <p className="mt-1 text-sm">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      selectedProvider.verified
                        ? 'bg-green-100 text-green-800'
                        : 'bg-orange-100 text-orange-800'
                    }`}>
                      {selectedProvider.verified ? 'Verified' : 'Pending Verification'}
                    </span>
                  </p>
                </div>
              </div>

              {selectedProvider.bio && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Bio</label>
                  <p className="mt-1 text-sm text-gray-900">{selectedProvider.bio}</p>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700">Joined</label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(selectedProvider.created_at).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => handleVerifyProvider(selectedProvider.id, !selectedProvider.verified)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium ${
                    selectedProvider.verified
                      ? 'bg-orange-100 text-orange-800 hover:bg-orange-200'
                      : 'bg-green-100 text-green-800 hover:bg-green-200'
                  }`}
                >
                  {selectedProvider.verified ? 'Remove Verification' : 'Verify Provider'}
                </button>
                
                <button
                  onClick={() => handleDeleteProvider(selectedProvider.id)}
                  className="px-4 py-2 bg-red-100 text-red-800 rounded-lg text-sm font-medium hover:bg-red-200"
                >
                  Delete Provider
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ProviderManagement
