import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { Eye, EyeOff, Loader2 } from 'lucide-react'
import { supabase } from '../lib/supabase'

export default function SignIn() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({})
  const navigate = useNavigate()

  const validateForm = () => {
    const errors: {[key: string]: string} = {}

    if (!email.trim()) {
      errors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      errors.email = 'Please enter a valid email address'
    }

    if (!password.trim()) {
      errors.password = 'Password is required'
    } else if (password.length < 6) {
      errors.password = 'Password must be at least 6 characters'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log('SignIn form submitted with:', { email, password: '***' })

    if (!validateForm()) {
      return
    }

    setLoading(true)
    setError(null)
    setValidationErrors({})

    try {
      console.log('Attempting Supabase auth...')
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      console.log('Supabase auth response:', { data: data?.user?.email, error: error?.message })

      if (error) throw error

      console.log('Auth successful, navigating to dashboard...')
      // Navigate to dashboard on success
      navigate('/dashboard')
    } catch (error) {
      console.error('Auth error:', error)
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex flex-col justify-center py-8 sm:px-6 lg:px-8" style={{ backgroundColor: 'var(--bg-primary)' }}>
      <div className="sm:mx-auto sm:w-full sm:max-w-lg">
        <div className="flex justify-center">
          <div
            className="rounded-xl px-6 py-3 text-2xl font-light tracking-wide"
            style={{
              backgroundColor: 'var(--primary)',
              color: 'var(--bg-primary)',
              boxShadow: 'var(--shadow-card)',
              minWidth: '80px',
              textAlign: 'center',
              letterSpacing: '0.1em'
            }}
          >
            care
          </div>
        </div>
        <h2 className="mt-6 text-center text-xl sm:text-2xl font-light" style={{ color: 'var(--text-primary)' }}>
          Sign in to your account
        </h2>
        <p className="mt-2 text-center text-xs sm:text-sm" style={{ color: 'var(--text-secondary)' }}>
          Or{' '}
          <Link
            to="/get-started"
            className="font-medium transition-colors duration-150"
            style={{
              color: 'var(--primary)',
              textDecoration: 'none'
            }}
          >
            create a new account
          </Link>
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-lg">
        <div
          className="py-8 px-8 rounded-xl border"
          style={{
            backgroundColor: 'var(--bg-primary)',
            borderColor: 'var(--border-light)',
            boxShadow: 'var(--shadow-card)'
          }}
        >
          <form className="space-y-6" onSubmit={handleSignIn} noValidate>
            {error && (
              <div
                className="rounded-xl p-4 border"
                style={{
                  backgroundColor: 'var(--bg-error)',
                  borderColor: 'var(--border-error)'
                }}
              >
                <p className="text-sm" style={{ color: 'var(--text-error)' }}>{error}</p>
              </div>
            )}
            
            <div>
              <label htmlFor="email" className="block text-base font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                Email address
              </label>
              <div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value)
                    if (validationErrors.email) {
                      setValidationErrors(prev => ({...prev, email: ''}))
                    }
                  }}
                  className="appearance-none block w-full px-4 py-4 border rounded-xl focus:outline-none transition-all duration-200"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  placeholder="Enter your email"
                  aria-describedby={validationErrors.email ? "email-error" : undefined}
                  aria-invalid={!!validationErrors.email}
                />
              </div>
              {validationErrors.email && (
                <p id="email-error" className="mt-1 text-sm" style={{ color: 'var(--text-error)' }} role="alert">
                  {validationErrors.email}
                </p>
              )}
            </div>

            <div>
              <label htmlFor="password" className="block text-base font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                Password
              </label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => {
                    setPassword(e.target.value)
                    if (validationErrors.password) {
                      setValidationErrors(prev => ({...prev, password: ''}))
                    }
                  }}
                  className="appearance-none block w-full px-4 py-4 pr-12 border rounded-xl focus:outline-none transition-all duration-200"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 transition-all duration-200 hover:bg-opacity-10"
                  style={{ 
                    color: 'var(--text-muted)',
                    borderRadius: '4px',
                    padding: '4px'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.color = 'var(--primary)'
                    e.currentTarget.style.backgroundColor = 'var(--bg-accent)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.color = 'var(--text-muted)'
                    e.currentTarget.style.backgroundColor = 'transparent'
                  }}
                  aria-label={showPassword ? 'Hide password' : 'Show password'}
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="text-sm">
                <Link
                  to="/forgot-password"
                  className="font-medium transition-all duration-200"
                  style={{ 
                    color: 'var(--primary)',
                    textDecoration: 'none'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.color = 'var(--primary)'
                    e.currentTarget.style.textDecoration = 'underline'
                    e.currentTarget.style.opacity = '0.8'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.color = 'var(--primary)'
                    e.currentTarget.style.textDecoration = 'none'
                    e.currentTarget.style.opacity = '1'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                >
                  Forgot your password?
                </Link>
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="button-primary w-full flex justify-center py-4 px-6 rounded-xl text-base font-semibold transition-all duration-200 disabled:opacity-50 hover:opacity-90"
                style={{
                  boxShadow: 'var(--shadow-card)'
                }}
                onFocus={(e) => {
                  e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                }}
                onBlur={(e) => {
                  e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                }}
              >
                {loading ? (
                  <div className="flex items-center justify-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Signing in...
                  </div>
                ) : (
                  'Sign in'
                )}
              </button>
            </div>
          </form>

          <div className="mt-8">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t" style={{ borderColor: 'var(--border-light)' }} />
              </div>
              <div className="relative flex justify-center text-sm">
                <span
                  className="px-4 py-1 rounded-md"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    color: 'var(--text-secondary)',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}
                >
                  Or continue with
                </span>
              </div>
            </div>

            <div className="mt-8">
              <button
                type="button"
                className="button-secondary w-full flex justify-center py-4 px-6 border rounded-xl text-base font-semibold transition-all duration-200"
                style={{
                  borderColor: 'var(--border-medium)',
                  backgroundColor: 'var(--bg-primary)',
                  color: 'var(--text-primary)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
                  e.currentTarget.style.borderColor = 'var(--primary)'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                  e.currentTarget.style.borderColor = 'var(--border-medium)'
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = 'var(--primary)'
                  e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = 'var(--border-medium)'
                  e.currentTarget.style.boxShadow = 'none'
                }}
                onClick={() => {
                  // Google OAuth would go here
                  console.log('Google OAuth not implemented yet')
                }}
              >
                Continue with Google
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
